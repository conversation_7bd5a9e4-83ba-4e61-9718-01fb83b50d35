# Agent 技术实现规范 (AutoPilot AI)

**版本**: 1.0
**状态**: 草案

## 1. 综述

本文档旨在为 **AutoPilot AI (领航AI)** 项目中的多智能体（Multi-Agent）系统提供统一、明确的技术实现规范。规范将基于 **Microsoft AutoGen** 框架，并融合项目已有的 **《融合系统架构》** 设计理念以及从 **`deer-flow-main`** 项目中借鉴的最佳实践。

本文档是开发人员实现所有Agent相关功能的核心指导，旨在确保代码的一致性、可维护性和可扩展性。

---

## 2. 核心技术框架: Microsoft AutoGen

系统将全面采用 `Microsoft AutoGen` 作为底层的 Agent 构建与会话管理框架。

### 2.1. 核心组件映射

| 领航AI架构概念 | AutoGen 实现组件 | 备注 |
| :--- | :--- | :--- |
| **智能体 (Agent)** | `autogen_agentchat.agents.AssistantAgent` | 所有自定义Agent的基类，拥有独立的system_message, llm_config, 和工具集。 |
| **用户代理** | `autogen_agentchat.agents.UserProxyAgent` | 用于接收用户输入、执行代码或工具，以及作为人工审核（Human-in-the-Loop）的接入点。 |
| **简单任务协同** | `agent.initiate_chat(recipient, ...)` | 用于两个Agent之间的直接对话，适用于链路一的简单任务。 |
| **复杂任务协同** | `autogen_agentchat.teams.GroupChat` | `GroupChat` (如 `RoundRobinGroupChat`, `SelectorGroupChat`) 与 `GroupChatManager` (或自定义图编排器) 将用于管理多个专业Agent的协同工作，对应链路二。 |
| **工具 (Tools)** | `autogen_core.tools.FunctionTool` | 所有外部API（地图、天气、数据库检索等）都应封装为函数，并通过此类注册给指定的Agent。 |

### 2.A. 开发优先级与迭代策略

为确保项目平稳、高效地推进，开发将遵循以下优先级顺序：

1.  **第一阶段：核心Agent框架搭建**
    *   实现 `PlannerAgent`, `ExecutorAgent`, `ResponseAgent` 的基本功能。
    *   建立基于 `ToolRegistry` 的可插拔工具架构。
    *   搭建基于 `pydantic-settings` 的统一配置系统。
    *   实现基于 `PromptManager` 的提示词管理和加载机制。
    *   **目标**: 能够运行一个完整的、但无记忆、无复杂依赖的端到端任务流程。

2.  **第二阶段：核心服务工具化接入**
    *   **日志 (Logging)**: 实现统一的日志工具，供Agent在关键节点调用，记录执行信息。
    *   **状态与输出 (State & Output)**: 实现与Redis交互及通过SSE输出的工具，使Agent能够管理任务状态并向客户端发送标准格式的事件。
    *   **数据库 (Database)**: 接入基础的数据库工具，用于简单的数据查询与持久化。

3.  **第三阶段：记忆系统实现**
    *   在现有架构基础上，逐步实现L1、L2、L3分层记忆，并通过 `MemoryManager` 工具集接入Agent。

此迭代策略的核心思想是 **"框架先行，功能后填"**。我们首先构建一个稳定、可扩展的骨架，然后将日志、记忆、数据库等复杂功能作为标准化的"插件"（即工具）逐步填充进去，确保每一步的交付都是健壮和可测试的。

---

## 3. Agent 详细设计与实现规范

### 3.1. Agent 基础结构

所有自定义 Agent 类都应在 `src/agents/` 目录下创建。每个Agent在实例化时，必须提供以下核心参数：

*   **`name`**: Agent 的唯一标识符，应清晰、简洁（例如, `planner_agent`, `researcher`）。
*   **`system_message`**: 定义Agent角色、职责、能力和输出格式的核心提示词。**严禁**将长篇 system_message 硬编码在 Python 文件中，所有提示词必须遵循 **第6章：提示词工程规范**。
*   **`llm_config`**: LLM的配置。此配置**不应**直接在Agent实例化时硬编码创建，而应通过**统一配置管理器**根据Agent的角色（如`reasoning`, `basic`）获取。配置内容遵循OpenAI标准接口，包含`base_url`, `api_key`, `model`等关键信息。
*   **`tools`**: 一个 `FunctionTool` 的列表，包含了该Agent被授权使用的所有工具。

### 3.2. 核心 Agent 职责定义

#### 3.2.1. `PlannerAgent` (规划器)
*   **职责**: 作为复杂任务的起点，负责将用户模糊的请求分解成一个结构清晰、可执行的 **Plan**。
*   **实现**: 继承 `AssistantAgent`。其 `system_message` 必须严格遵循 `deer-flow-main` 中 `planner.md` 的设计思想，包含**角色定义、分析框架、严格规则和强制的JSON输出格式**。
*   **输出**: 必须输出一个符合 `src.models.plan.Plan` Pydantic 模型的JSON对象（详见4.2.）。

#### 3.2.2. `ExecutorAgent` (执行器 - 泛指专业Agent)
*   **职责**: 负责执行 `Plan` 中的某一个具体步骤。这是一个泛指，包含了 `ResearcherAgent`, `ItineraryAgent` 等所有专业技能Agent。
*   **实现**: 继承 `AssistantAgent`。其 `system_message` 应非常专注，只描述其单一职责（例如，"你是一个旅游信息研究员，你的任务是根据给定的标题和描述，使用网页搜索工具查找信息"）。
*   **工具**: 每个执行器应只被赋予完成其任务所必需的最少工具。

#### 3.2.3. `ResponseAgent` (响应生成器)
*   **职责**: 在所有任务步骤执行完毕后，负责汇总所有Agent的执行结果和观察，生成最终面向用户的、统一格式的响应。
*   **实现**: 继承 `AssistantAgent`。其 `system_message` 专注于将结构化的数据（如行程计划、研究报告）转化为流畅、友好、个性化的自然语言，并封装成标准JSON格式。
*   **输出**: 必须输出符合 **`doc/API及通信协议规范.md`** 中定义的 `task_completed` 事件的 `final_response` 格式。

### 3.3. 复杂任务图编排实现要点

**核心思想**：我们架构中的"图编排引擎 (Graph Orchestrator)"并非一个独立的外部组件，而是通过巧妙地运用 AutoGen 自身机制来实现的，核心在于 `SelectorGroupChat` 和一个自定义的**决策函数**。

*   **实现方式**:
    1.  复杂任务应由一个 `autogen_agentchat.teams.SelectorGroupChat` 来管理。
    2.  为该 `SelectorGroupChat` 提供一个自定义的 `candidate_func` (候选人函数) 或 `selector_func` (选择器函数)。
    3.  这个函数是整个"图"的大脑。在每一轮对话中，它都会被调用，其职责是：
        *   读取 **Redis** 中存储的当前任务的完整 `AgentState` (特别是 `current_plan` 的状态)。
        *   分析当前的对话历史。
        *   根据 `Plan` 中定义的步骤依赖关系和当前各步骤的完成状态，**以编程方式决定**下一个应该发言的Agent是谁。
    4.  通过这种方式，我们可以实现条件分支（如果步骤A失败，则调用错误处理Agent）、并行（如果多个步骤无依赖，理论上可创建并行子任务）和依赖管理（步骤B必须在步骤A完成后才能开始），从而动态地构建出一个执行图。

### 3.4. 工具 (Tools) 设计原则

为保证系统的模块化和工具的可复用性，所有提供给 Agent 使用的工具（`FunctionTool`）必须遵循以下原则：

*   **原子性 (Atomic)**: 每个工具函数应只做一件定义明确、范围狭小的事情。例如，`get_weather(city: str)` 只负责获取天气，不应包含推荐穿衣指数的逻辑。
*   **无状态 (Stateless)**: 工具函数自身不应存储任何与单次任务相关的信息。它所需的所有输入都必须通过函数参数明确传入。它不应该直接访问或依赖于全局的 `AgentState`。
*   **上下文无关**: 工具的实现应与任何特定的业务流程或任务计划解耦。Agent 负责理解上下文并决定如何调用工具，而工具只负责执行并返回结果。

---

## 4. 上下文注入与状态管理

上下文和状态管理是实现多Agent协同和长流程任务的关键。

### 4.1. `AgentState` 数据模型
我们将定义一个全局的 `AgentState` Pydantic模型，用于在不同Agent和不同流程节点之间传递信息。

```python
# In: src/models/state.py
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from .plan import Plan # Assumes plan.py exists

class AgentState(BaseModel):
    task_id: str = Field(..., description="唯一的任务ID")
    original_request: str = Field(..., description="用户的原始请求")
    
    # 附件信息
    attachments: List[Dict[str, Any]] = Field(default_factory=list, description="任务关联的附件列表，例如: [{'asset_id': '...', 'type': 'image'}]")
    
    # 上下文信息
    context: Dict[str, Any] = Field(default_factory=dict, description="环境上下文, 如时间、地点、用户偏好")
    
    # 规划与执行
    current_plan: Optional[Plan] = Field(None, description="由PlannerAgent生成的执行计划")
    execution_history: List[Dict[str, Any]] = Field(default_factory=dict, description="各步骤的执行结果和观察")
    
    # 最终结果
    final_response: Optional[Dict[str, Any]] = Field(None, description="由ResponseAgent生成的最终JSON输出")
    
    # 状态标记
    status: str = Field("pending", description="任务状态: pending, planning, executing, finished, error")
```

### 4.2. 状态传递机制
*   **简单对话 (`initiate_chat`)**: `AgentState` 的相关部分可以序列化为字符串，作为初始消息或在对话中传递。
*   **复杂图编排**: 对于链路二的复杂任务，`AgentState` 的完整实例将存储在 **Redis** 中，以 `task_id` 为键。图编排引擎负责在调度每个Agent节点前后，更新和读取Redis中的`AgentState`。Agent本身通过工具或特定函数接口与Redis交互获取所需上下文。

### 4.3. AgentState 与内部消息的关系

必须明确，我们设计的全局 `AgentState` (存储于Redis) 是对 AutoGen 内部会话历史的**补充和增强**，而非替代。

*   **AutoGen 内部消息 (`groupchat.messages`)**:
    *   **作用**: 存储当前 `GroupChat` 内的**局部对话历史**。它反映了Agent之间轮次分明的对话流。
    *   **访问**: Agent可以直接在对话中看到，是其理解"当前聊到哪了"的主要依据。

*   **全局 `AgentState` (Redis)**:
    *   **作用**: 存储整个任务的**全局状态和宏观视图**。它包含了完整的执行计划、所有分支任务的成果、以及需要跨流程共享的任何信息。
    *   **访问**: Agent需要通过一个专门的工具（例如 `get_task_state_from_redis(task_id: str)`）来按需获取。当Agent需要了解"整个任务进行到哪一步了"或"其他团队成员的工作成果是什么"时，才会调用此工具。

这种**"局部对话历史 + 全局状态总线"**的组合，既能让Agent专注于当前对话，又赋予了它们获取全局视野的能力，避免了信息混乱。

---

## 5. 可扩展分层记忆架构 (Extensible, Layered Memory Architecture)

为支持记忆系统从简单到复杂的逐步演进，并确保架构的长期可扩展性，我们设计一个分层、可插拔的记忆架构。

### 5.1. 核心设计原则

*   **分步实现 (Phased Implementation)**: 承认记忆系统是复杂的，允许其功能分阶段上线。初始版本可能只包含L0/L1记忆，但架构必须为L2/L3的无缝接入预留接口。
*   **统一接口 (Unified Interface)**: Agent不应直接与具体的数据库（Redis, VectorDB, MySQL）交互来获取记忆。所有记忆的读取、写入和管理都必须通过一个统一的`MemoryManager`进行。
*   **按需检索 (On-demand Retrieval)**: 记忆的检索不应该是无脑的全量加载，而应由`PlannerAgent`或专门的Agent根据当前任务上下文，智能地决定何时、何种以及何等深度的记忆是必需的。

### 5.2. 分层记忆模型 (Layered Memory Model)

| 层级 | 名称 | 描述 | 实现技术 | 注入方式 |
| :--- | :--- | :--- | :--- | :--- |
| **L0** | **即时上下文 (Immediate Context)** | 用户当前的直接请求和该轮对话中提供的明确信息。 | 内存变量 | 直接作为Agent初始输入。 |
| **L1** | **短期/会话记忆 (Short-term/Session)** | 当前任务或会话的完整上下文，包括多轮对话历史、中间步骤结果。 | Redis | 存储在`AgentState`中，通过Redis在各Agent间共享。 |
| **L2** | **长期/情景记忆 (Long-term/Episodic)** | 历史上的具体对话、事件和任务。用于回忆"我们上次聊了什么"。 | 向量数据库 (Vector DB) | `MemoryManager`按需进行语义搜索后，将结果注入`AgentState`或直接提供给Agent。 |
| **L3** | **用户画像/语义记忆 (User Profile/Semantic)** | 经过提炼和抽象的用户偏好、习惯、核心事实。用于理解"用户是一个什么样的人"。 | SQL / NoSQL DB | `MemoryManager`按需查询后，将结果注入`AgentState`或直接提供给Agent。 |

#### 5.2.1. L2 记忆检索流程 (Embedding + Reranker)

为实现高效、精准的L2长期记忆检索，`MemoryManager` 将采用先进的两阶段检索（Two-Stage Retrieval）架构。这是一个后期实现的功能，但架构上必须预留接口。

**第一阶段：召回 (Recall) - 使用 Embedding 模型**
1.  **记忆存储**: 当需要长期存储一段信息时（如任务总结、用户反馈），`MemoryManager` 会调用 `Embedding` 模型将该文本转换为一个高维向量。这个向量与原始文本一同被存入向量数据库。
2.  **记忆检索**: 当需要检索记忆时，`MemoryManager` 首先用同一个 `Embedding` 模型将用户的当前查询转换为查询向量。然后，它在向量数据库中执行高效的近似最近邻（ANN）搜索，快速找出与查询向量在语义上最相似的一批（例如 Top 50）候选记忆。
*   **核心作用**: `Embedding` 模型负责从海量记忆中进行快速、广泛的语义筛选，确保不漏掉任何可能相关的记忆。

**第二阶段：精排 (Rerank) - 使用 Reranker 模型**
1.  **二次排序**: `Embedding` 模型召回的候选列表虽然相关，但其排序不一定是最优的。`MemoryManager` 会将这个候选列表（连同原始查询）传递给 `Reranker` 模型。
2.  **精准打分**: `Reranker` 模型使用更复杂的计算（如交叉注意力）来精确评估每一个候选记忆与当前查询的真实关联度，并为它们重新打分。
3.  **最终筛选**: `MemoryManager` 根据 `Reranker` 的打分对列表进行重新排序，并选取得分最高的几个（例如 Top 3）作为最终的、最相关的记忆，注入到Agent的上下文中。
*   **核心作用**: `Reranker` 模型负责对初步筛选的结果进行精加工，极大地提升最终提供给Agent的记忆的"信噪比"，使其决策基于最精准的上下文。

通过"Embedding召回 + Reranker精排"的两阶段流程，系统能够在保证高效率的同时，实现远超单一`Embedding`模型的检索精度。

### 5.3. 统一记忆管理器 (`MemoryManager`)

*   **职责**: 作为所有记忆操作的唯一入口点。它封装了与不同后端存储（Redis, VectorDB等）交互的复杂逻辑，并在后期实现L2记忆时，内部集成`Embedding`和`Reranker`模型。
*   **实现**: 可以是一个专门的`MemoryManager`类或一个专门的`MemoryAgent`。它提供清晰的接口方法，如：
    *   `get_short_term_memory(task_id: str) -> Dict`
    *   `search_long_term_memory(task_id: str, query: str, top_k: int) -> List[str]` (初期可返回空列表)
    *   `get_user_profile(user_id: str) -> Dict` (初期可返回空字典)
*   `PlannerAgent` 和其他需要记忆的Agent，通过调用`MemoryManager`提供的工具来获取记忆，而不是自己连接数据库。

### 5.4. 架构占位与扩展性设计

为了在开发初期就为记忆系统预留扩展能力，我们需要做到以下几点：

1.  **`AgentState`中的占位**: `AgentState`模型中应预留记忆字段，即使初期它们可能为空。
    ```python
    # In: src/models/state.py
    class AgentState(BaseModel):
        # ... other fields
        retrieved_memory: Dict[str, Any] = Field(default_factory=dict, description="从L2/L3检索到的记忆片段")
        # ...
    ```

2.  **`PlannerAgent`提示词中的占位**: `planner.md`的提示词中应包含处理记忆的指令，并注入记忆变量，即使初期该变量是空的。这能让LLM从一开始就学会在有记忆上下文的情况下进行思考。
    ```markdown
    # In src/prompts/templates/planner.md

    ## Context Assessment

    Here is the memory retrieved from past interactions:
    {{ retrieved_memory_summary }}

    Based on the user's request and the retrieved memory, assess if there is sufficient context...
    ```

3.  **工具集中的占位**: 为`PlannerAgent`注册`MemoryManager`提供的工具（如`search_long_term_memory`），即使这些工具在初期只是返回空结果的"桩函数 (Stub Function)"。

通过这些占位设计，当未来要实现L2或L3记忆时，我们只需要去实现`MemoryManager`中对应的方法和后端的数据库，而几乎不需要修改任何Agent的核心逻辑和提示词，从而实现了平滑、低成本的扩展。

### 5.5. 与AutoGen内部状态的衔接

**核心思想**：我们设计的 `MemoryManager` 是对 AutoGen 内置状态管理机制（如 `agent.save_state()`）的专业化和生产级增强。

*   **AutoGen `save_state`**: `AutoGen` 提供了将单个Agent或`GroupChat`的**完整对话历史**保存到JSON文件的能力。这对于简单的会话恢复很有用。
*   **我们的 `MemoryManager`**: 我们的系统需要更精细化的、跨任务的、多层次的记忆。`MemoryManager` 通过与Redis, VectorDB等后端集成，实现了这一点。
*   **衔接方式**: 在实践中，我们主要依赖 `MemoryManager` 和存储在Redis中的 `AgentState`。仅在需要进行完整会话调试或快照备份的特殊场景下，才会考虑使用 `save_state` 作为辅助手段。

---

## 6. 统一输出与通信机制

### 6.1. `Plan` 输出框架
`PlannerAgent` 的输出必须是结构化的 `Plan` 对象，其Pydantic模型定义如下：

```python
# In: src/models/plan.py
from pydantic import BaseModel, Field
from typing import List

class Step(BaseModel):
    step_id: int
    title: str
    description: str
    agent_role: str # 需要执行此步骤的Agent角色, e.g., "ResearcherAgent"
    status: str = "pending" # pending, in_progress, completed, failed

class Plan(BaseModel):
    thought: str = Field(..., description="PlannerAgent的思考过程")
    steps: List[Step] = Field(..., description="分解后的任务步骤列表")
```

### 6.2. 通信机制
项目的通信机制遵循 **`doc/API及通信协议规范.md`** 中定义的 **SSE + HTTP POST** 复合型双向通信模型。所有负责与前端交互的Agent（特别是`ResponseAgent`）都必须严格按照该规范生成事件和数据。

### 6.3. 输出工具化 (Output as a Tool)

为将业务逻辑与通信协议解耦，所有面向客户端的输出（如发送SSE事件）都应通过一个专用的 **`OutputTool`** 来完成。

*   **职责**: `OutputTool` 封装了所有与SSE通信相关的实现细节。Agent只需调用此工具并提供标准化的数据负载（例如，一个Pydantic模型实例）。
*   **优点**:
    *   **逻辑清晰**: Agent的核心职责是思考和执行任务，而不是处理通信协议。
    *   **易于测试**: 可以轻松地模拟`OutputTool`，从而在单元测试中验证Agent是否生成了正确的输出内容，而无需启动一个真实的Web服务。
    *   **便于替换**: 如果未来通信协议需要变更（例如，从SSE切换到WebSocket），只需修改`OutputTool`的内部实现，而无需改动任何Agent的代码。

---

## 7. 提示词工程规范

借鉴 `deer-flow-main` 的成功实践，建立严格的提示词工程规范。

### 7.1. 目录结构
*   所有Agent的 `system_message` 模板必须存放在 `src/prompts/` 目录下。
*   文件名应与Agent角色对应，例如 `planner.md`, `researcher.md`。

### 7.2. 模板引擎
*   采用 **Jinja2** 作为提示词的模板引擎，以支持动态变量注入。

### 7.3. 管理与加载

*   创建一个 `PromptManager` 单例或工具类，位于 `src/prompts/manager.py`。
*   `PromptManager` 负责：
    1.  在服务启动时，加载所有 `.md` 模板文件到内存中。
    2.  提供一个 `render(template_name: str, **kwargs) -> str` 方法。
    3.  此方法接收模板名称和需要注入的上下文变量（例如，从`AgentState`中提取的信息），返回最终渲染好的、可直接供Agent使用的提示词字符串。

#### 7.3.1. 全局动态变量注入
**设计原则**: 为简化Agent的开发，`PromptManager` 在渲染模板时，应自动注入一组全局可用的动态变量，而无需每个Agent都去手动获取。这些变量是Agent感知环境的基础。
*   **实现**: 在`PromptManager.render`方法中，除了接收外部传入的`kwargs`，还应自动准备一个包含通用上下文的字典，并将其合并。
*   **必须注入的变量**:
    *   `CURRENT_DATETIME`: 当前的日期和时间，格式为 `YYYY-MM-DD HH:MM:SS`。
*   **示例**:
    ```python
    # In src/prompts/manager.py (conceptual)
    def render(self, template_name: str, **kwargs) -> str:
        global_context = {
            "CURRENT_DATETIME": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # 可以添加更多全局信息...
        }
        final_context = {**global_context, **kwargs}
        template = self.env.get_template(f"{template_name}.md")
        return template.render(**final_context)
    ```

### 7.4. 编写原则
*   **角色化**: 明确赋予Agent一个角色。
*   **规则化**: 设定清晰的指令、约束和禁止项。
*   **格式化**: 在提示词中明确要求输出的格式，并提供示例（如JSON Schema或TypeScript接口定义）。
*   **示例化 (Few-shot)**: 对于复杂的格式要求，可以在提示词中提供1-2个输入/输出的示例。

---

## 8. 可扩展工具架构 (Pluggable Tool Architecture)

为确保系统具备强大的扩展能力，能够无缝接入不同类型的能力（无论是简单的本地函数，还是复杂的外部MCP服务），我们设计一个统一的、可插拔的工具抽象层。

### 8.1. 核心设计原则

*   **万物皆工具**: 无论能力的底层实现是什么，都必须将其封装成一个标准的、可被AutoGen识别的 `FunctionTool` 对象。Agent本身不关心工具的内部实现，只关心其名称、描述和参数Schema。
*   **注册而非硬编码**: 严禁在Agent或编排逻辑中硬编码工具列表。所有工具都应通过一个中央注册表进行动态发现和加载，实现"即插即用"。
*   **核心服务工具化**: 项目中的核心服务，如**日志记录、记忆读写、状态输出(SSE)**，都必须实现为标准工具。这确保了Agent与基础设施的解耦，并使得所有核心操作都是可测试、可替换的。

#### 8.1.1. 工具分类
我们的工具集主要分为两类：

1.  **本地函数调用 (Local Function Call)**:
    *   **定义**: 直接在项目代码库中实现的Python函数。
    *   **用途**: 用于执行确定性的、本地的、高频的任务，例如数据转换、内部状态访问、调用`OutputTool`等。
    *   **示例**: `get_task_state_from_redis`, `calculate_travel_time`。

2.  **多能力平台 (Multi-Capability Platform, MCP)**:
    *   **定义**: 对外部复杂服务或API的封装调用。其设计思想借鉴了 `context7` 等平台的实践，将外部世界的复杂能力抽象成一个简单的接口。
    *   **用途**: 用于执行需要复杂计算、访问海量数据或与外部世界交互的任务。
    *   **示例**: 调用外部API进行机票酒店预订、执行代码沙箱、进行复杂的图像分析等。

#### 8.1.2. 与AutoGen底层工具的衔接
**核心思想**: 我们设计的`ToolRegistry`是AutoGen工具机制的**上层封装和管理器**。
*   `autogen`通过在`AssistantAgent`的构造函数中传入一个`tools`列表（其中每个元素都是`FunctionTool`实例或可调用函数）来为Agent配备工具。
*   我们的`ToolRegistry.get_tools(["tool_a", "tool_b"])`方法正是为了动态地、按需地构建这个`tools`列表，从而避免在代码中硬编码。这使得我们的架构与`AutoGen`的底层机制完美兼容且更具工程化。
*   对于外部MCP服务，`AutoGen`提供了`McpWorkbench`等高级工具。我们的`ToolRegistry`同样可以将其封装。开发者只需编写一个调用`McpWorkbench`的包装函数，并用`@register_tool`装饰，即可将其无缝集成到我们的统一工具体系中。

### 8.2. 核心组件：ToolRegistry

我们将实现一个 `ToolRegistry` 单例，并提供一个 `@register_tool` 装饰器。

*   **开发者**: 新增一个工具时，只需在 `src/tools/` 目录下创建一个新文件，编写工具函数并用 `@register_tool` 标记。
*   **系统**: 在服务启动时，系统会自动扫描 `src/tools/` 目录，发现所有已注册的工具函数，并将其加载到注册表中。
*   **Agent实例化**: Agent的 `tools` 参数将通过从 `ToolRegistry` 按名称获取 `FunctionTool` 对象来动态构建。

**示例实现 (`src/tools/registry.py`)**:
```python
# 这部分是框架的核心，一次性写好
from typing import Dict, List, Callable, Any
from autogen_core.tools import FunctionTool
import inspect

class ToolRegistry:
    def __init__(self):
        self._tools: Dict[str, Callable] = {}

    def register(self, func: Callable) -> Callable:
        """一个用于注册工具函数的装饰器"""
        name = func.__name__
        if name in self._tools:
            raise ValueError(f"Tool with name '{name}' already registered.")
        self._tools[name] = func
        return func

    def get_tool(self, name: str) -> FunctionTool:
        """按名称获取单个AutoGen FunctionTool"""
        if name not in self._tools:
            raise ValueError(f"Tool '{name}' not found.")
        return FunctionTool.from_function(self._tools[name])

    def get_tools(self, names: List[str]) -> List[FunctionTool]:
        """按名称列表获取多个AutoGen FunctionTool"""
        return [self.get_tool(name) for name in names]

# 创建一个全局单例
tool_registry = ToolRegistry()

# 方便导入
register_tool = tool_registry.register
```

### 8.3. 工具扩展示例 (MCP接入)

当需要接入一个外部MCP服务时，我们只需创建一个包装函数。

**示例 (`src/tools/mcp_services/travel_service.py`)**:
```python
from ..registry import register_tool
import requests # 举例

@register_tool
def invoke_travel_mcp(destination: str, days: int) -> Dict:
    """
    调用外部旅游MCP服务来规划行程。
    :param destination: 目的地城市。
    :param days: 行程天数。
    :return: MCP服务返回的行程规划JSON。
    """
    # 此处为调用外部MCP的真实逻辑
    # mcp_endpoint = "https://api.mcp-provider.com/v1/travel"
    # ...
    # return response.json()
    
    # 返回一个模拟的成功响应
    return {"status": "success", "plan": f"已成功规划{days}天在{destination}的行程。"}
```
通过这种方式，Agent调用 `invoke_travel_mcp` 和调用任何其他本地函数完全一样，实现了能力的无差别扩展。

---

## 9. 单元测试规范 (Unit Testing Specification)

为保证代码质量和系统稳定性，所有核心模块，特别是Agent和Tool，都必须编写单元测试。

### 9.1. 同步原则 (Synchronization Principle)

*   **一一对应原则**: 对于 `src/` 源码目录下的每一个功能模块 `module.py`，都**必须**在 `tests/` 目录下存在一个对应的测试文件 `test_module.py`。
*   **测试先行**: 任何新功能的开发或Bug修复，都**必须**从编写一个或多个单元测试开始。这个初始测试应该能复现Bug或定义新功能，并且在功能实现前是失败的。
*   **完成的定义 (Definition of Done)**: 一个功能模块或一个开发任务，只有在**功能代码**和**完整的单元测试**（覆盖主要逻辑、边界条件和异常处理）都已编写完成并通过时，才被认为是"已完成"。没有测试的代码将被视为未完成的半成品。

### 9.2. 测试框架与目录结构

*   **框架**: 统一使用 `pytest` 作为测试框架。
*   **根目录**: 所有测试代码必须放在项目根目录下一个名为 `tests/` 的独立文件夹中。此文件夹不应嵌套在 `src/` 源码目录内。
*   **镜像结构**: `tests/` 目录的内部结构必须严格镜像 `src/` 的目录结构。这样可以清晰地将测试代码与被测试的源代码对应起来。

**目录结构示例**:
```
src/
├── agents/
│   ├── __init__.py
│   └── researcher.py
├── tools/
│   ├── __init__.py
│   └── web_search.py
│
tests/
├── agents/
│   ├── __init__.py
│   └── test_researcher.py  # 测试 src/agents/researcher.py
└── tools/
    ├── __init__.py
    └── test_web_search.py  # 测试 src/tools/web_search.py
```
*   **命名约定**: 测试文件名必须以 `test_` 开头，测试函数名也必须以 `test_` 开头，这是 `pytest` 的标准发现机制。

### 9.3. 核心原则：模拟依赖

单元测试的核心是**验证代码的逻辑**，而不是测试外部依赖（如LLM、数据库、外部API）的稳定性。因此，所有外部依赖都**必须**被模拟（Mock）。

*   **模拟工具**: 使用 `unittest.mock` 库中的 `patch` 或 `MagicMock`。
*   **LLM调用**: 必须模拟LLM Client，使其在测试中返回一个预先定义好的、符合预期的响应（例如，一个包含特定`tool_calls`的回复）。
*   **工具执行**: 测试Agent逻辑时，其调用的工具函数本身也应被模拟，以验证Agent是否用正确的参数调用了正确的工具。

### 9.4. ExecutorAgent 测试示例

以下是一个测试 `ExecutorAgent` 的示例，验证其在收到任务后，能否正确地调用LLM并解析其意图，最终以正确的参数调用工具。

```python
# In: tests/agents/test_researcher_agent.py
import pytest
from unittest.mock import MagicMock, patch
from src.agents import ResearcherAgent # 假设的Agent

@pytest.fixture
def researcher_agent():
    """创建一个带有模拟LLM Client的ResearcherAgent实例"""
    mock_llm_client = MagicMock()
    # 可以进一步配置mock_llm_client.chat.completions.create.return_value
    agent = ResearcherAgent(name="test_researcher", llm_config={"client": mock_llm_client})
    return agent, mock_llm_client

@patch("src.tools.web.search.web_search") # 模拟工具函数
def test_researcher_calls_web_search(mock_web_search, researcher_agent):
    # 1. 准备
    agent, mock_llm_client = researcher_agent
    task_message = "请帮我搜索'AutoGen框架'的相关信息"
    
    # 2. 配置模拟LLM的返回值
    # 模拟LLM决定调用web_search工具
    mock_llm_response = MagicMock()
    mock_tool_call = MagicMock()
    mock_tool_call.function.name = "web_search"
    mock_tool_call.function.arguments = '{"query": "AutoGen框架"}'
    mock_llm_response.choices[0].message.tool_calls = [mock_tool_call]
    mock_llm_client.chat.completions.create.return_value = mock_llm_response
    
    # 配置模拟工具的返回值
    mock_web_search.return_value = "这是关于AutoGen的搜索结果"

    # 3. 执行
    # AutoGen中通常通过 groupchat.send() 或 agent.generate_reply() 来触发
    # 此处简化为直接调用一个假设的内部方法
    agent.process_message(task_message)

    # 4. 断言
    # 验证LLM被以正确的消息调用
    mock_llm_client.chat.completions.create.assert_called_once()
    # 验证web_search工具被以正确的参数调用
    mock_web_search.assert_called_with(query="AutoGen框架")
```

### 9.5. 基于记忆注入的Agent测试规范

**核心原则**: Agent的智能很大程度上体现在其利用记忆进行个性化和上下文感知的能力上。因此，必须系统性地测试Agent在接收到不同类型、不同层次的记忆注入后的行为是否符合预期。

**测试方法**:
*   **系统化场景**: 使用 `pytest.mark.parametrize` 为同一个测试函数提供多组不同的记忆上下文输入，从而高效地覆盖"无记忆"、"短期记忆"、"长期记忆"和"用户画像注入"等多种场景。
*   **模拟记忆源**: 测试的重点是Agent的**逻辑**，而非记忆存储的**实现**。因此，必须模拟(Mock) `MemoryManager`或其提供的工具，使其在测试中返回预先定义好的、代表不同记忆场景的数据。
*   **验证关键点**:
    1.  **Prompt验证**: 断言注入的记忆是否被正确地格式化并包含在了发送给LLM的最终提示词中。
    2.  **行为验证**: 断言Agent的最终行为或输出（例如，`PlannerAgent`生成的计划）是否因为记忆的注入而发生了符合预期的改变。

**`PlannerAgent` 记忆测试示例**:

以下示例展示了如何测试 `PlannerAgent` 在不同记忆场景下生成截然不同的旅行计划。

```python
# In: tests/agents/test_planner_agent_memory.py
import pytest
from unittest.mock import MagicMock, patch

# 假设的PlannerAgent和MemoryManager
from src.agents import PlannerAgent
from src.memory import memory_manager 

# 1. 定义不同的记忆场景
SCENARIOS = {
    "no_memory": {
        "memory_tool_returns": {"search_long_term_memory": [], "get_user_profile": {}},
        "expected_plan_keywords": ["北京"] # 基础计划
    },
    "with_long_term_memory": {
        "memory_tool_returns": {
            "search_long_term_memory": ["用户上次说北京的酒店太贵了"],
            "get_user_profile": {}
        },
        "expected_plan_keywords": ["经济型", "预算"] # 计划应考虑预算
    },
    "with_user_profile": {
        "memory_tool_returns": {
            "search_long_term_memory": [],
            "get_user_profile": {"travel_preference": "喜欢自然风光，讨厌人多的地方"}
        },
        "expected_plan_keywords": ["公园", "郊区", "避开人群"] # 计划应体现偏好
    },
}

@pytest.fixture
def planner_agent():
    """创建一个带有模拟LLM Client的PlannerAgent实例"""
    # ... (同之前的fixture)
    mock_llm_client = MagicMock()
    # 为PlannerAgent注册所需的模拟工具
    agent = PlannerAgent(name="test_planner", llm_config={"client": mock_llm_client})
    return agent, mock_llm_client

@pytest.mark.parametrize("scenario_name, scenario_data", SCENARIOS.items())
@patch.object(memory_manager, 'search_long_term_memory') # 模拟长期记忆工具
@patch.object(memory_manager, 'get_user_profile') # 模拟用户画像工具
def test_planner_adapts_to_injected_memory(
    mock_get_user_profile,
    mock_search_long_term_memory,
    planner_agent,
    scenario_name,
    scenario_data
):
    # 1. 准备 (Arrange)
    agent, mock_llm_client = planner_agent
    user_request = "帮我规划一个3天的北京行程"
    
    # 根据场景配置模拟工具的返回值
    mock_search_long_term_memory.return_value = scenario_data["memory_tool_returns"]["search_long_term_memory"]
    mock_get_user_profile.return_value = scenario_data["memory_tool_returns"]["get_user_profile"]

    # 模拟LLM会根据输入生成一个包含关键词的计划
    def mock_llm_logic(*args, **kwargs):
        prompt = kwargs["messages"][0]["content"]
        mock_response = MagicMock()
        # 简单模拟：如果prompt中有关键词，plan中也应该有
        plan_content = "基础计划：游览天安门。"
        if "酒店太贵了" in prompt:
            plan_content = "经济型计划：寻找预算友好的酒店。"
        if "自然风光" in prompt:
            plan_content = "自然计划：游览香山公园，避开人群。"
        
        # 模拟LLM返回一个Plan的JSON
        mock_response.choices[0].message.content = f'{{"thought": "思考过程", "steps": [{{"step_id": 1, "title": "行程", "description": "{plan_content}"}}]}}'
        return mock_response
        
    mock_llm_client.chat.completions.create.side_effect = mock_llm_logic

    # 2. 执行 (Act)
    # 假设agent有一个方法可以启动规划流程
    final_plan = agent.generate_plan(user_request)

    # 3. 断言 (Assert)
    # 验证记忆工具被调用
    mock_search_long_term_memory.assert_called_once()
    mock_get_user_profile.assert_called_once()
    
    # 验证最终生成的计划符合预期
    final_plan_text = final_plan.steps[0].description
    for keyword in scenario_data["expected_plan_keywords"]:
        assert keyword in final_plan_text, f"在场景'{scenario_name}'中, 计划应包含'{keyword}'"

### 9.6. 测试多模态输入 (Testing Multimodal Inputs)

对于依赖图像、音频等文件输入的功能，在自动化测试中必须**避免**任何对外部真实云存储（如S3, GCS）的依赖。我们采用以下两种策略来隔离依赖：

#### 策略一: 集成测试的伪存储服务 (Fake Storage for Integration Tests)

此策略用于测试从API端点到Agent执行的完整流程。

1.  **创建测试专用端点**: 在测试环境中，启用一个专用的文件上传端点，例如 `POST /_test/upload/{asset_id}`。该端点接收文件并将其存储在本地临时目录。
2.  **伪造上传URL**: Mock `POST /v1/uploads/request` 接口的实现，使其返回的 `upload_url` 指向第一步中创建的测试端点。
3.  **执行测试**: 测试脚本通过这套伪造的流程，将本地的测试文件"上传"并获取`asset_id`，然后用此`asset_id`调用`/v1/tasks`接口，触发完整的业务逻辑。

#### 策略二: 单元测试的工具模拟 (Tool-level Mocking for Unit Tests)

此策略用于独立测试某个Agent或工具的逻辑，是**首选**的单元测试方法。

1.  **识别数据接入点**: 确定系统中负责根据`asset_id`获取文件内容的函数（例如 `asset_manager.get_asset_content(asset_id)`）。
2.  **模拟接入点**: 在测试代码中，使用 `unittest.mock.patch` 来替换该函数的实现。
3.  **提供本地数据**: 让被Mock的函数在接收到测试用的`asset_id`时，不执行下载，而是直接从本地`tests/fixtures/`目录读取一个预置的测试文件，并返回其二进制内容。这使得被测试的Agent或工具能接收到所需的数据，而完全无需网络或文件系统之外的交互。

**示例**:
```python
# In: tests/tools/test_vision_analyzer.py
from unittest.mock import patch

@patch("src.assets.asset_manager.get_asset_content")
def test_vision_analyzer(mock_get_content):
    # 准备：让mock函数返回本地图片数据
    with open("tests/fixtures/test.jpg", "rb") as f:
        mock_get_content.return_value = f.read()

    # 执行：调用需要测试的函数
    # result = vision_analyzer.analyze("asset-fake-id")

    # 断言：验证逻辑是否正确处理了返回的图片数据
    mock_get_content.assert_called_once_with("asset-fake-id")
    # ...其他断言
```

---

## 10. 统一配置管理 (Unified Configuration Management)

为避免硬编码和敏感信息泄露，所有配置项必须进行集中化、结构化管理。

### 10.1. 配置文件结构

*   在项目根目录创建 `config/` 文件夹。
*   `config/default.yaml`: 存放所有非敏感的、默认的配置项。
*   `config/development.yaml`, `config/production.yaml`: 可选，用于覆盖不同环境下的特定配置。
*   `.env`: 存放所有敏感信息，如API密钥、数据库密码。此文件**严禁**提交到Git仓库。

### 10.2. 配置加载与角色区分

*   **技术选型**: 使用 `pydantic-settings` 库，它能无缝地从YAML文件、环境变量和`.env`文件加载配置，实现多环境的灵活切换。
*   **实现**: 创建一个 `src/core/config.py` 文件，定义一个继承自 `BaseSettings` 的 `Settings` 类。
*   **按角色区分LLM**: 为实现成本与性能的最佳平衡，配置系统必须支持按"角色"（如`reasoning`, `basic`）定义不同的LLM。`PlannerAgent`等需要复杂推理的Agent应使用`reasoning`模型，而简单的格式转换或摘要Agent可使用成本更低的`basic`模型。所有LLM配置均需遵循标准的OpenAI接口格式。

**示例 (`src/core/config.py`)**:
```python
# In: src/core/config.py
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Dict, Optional

class LLMConfig(BaseModel):
    """遵循OpenAI接口标准的LLM配置"""
    model: str
    api_key: str
    base_url: Optional[str] = None
    # 可以添加其他OpenAI兼容参数，如 temperature, max_tokens等
    api_version: Optional[str] = None 
    
class Settings(BaseSettings):
    # 通过 model_config 指定从 .env 文件加载
    model_config = SettingsConfigDict(env_file='.env', env_file_encoding='utf-8', extra='ignore')

    # 按角色区分的LLM配置
    # 这些配置将从 config/*.yaml 和 .env 文件中加载
    # 例如，可以在 .env 中定义 REASONING_LLM_API_KEY
    reasoning_llm: LLMConfig = Field(..., description="用于复杂推理的高级模型")
    basic_llm: LLMConfig = Field(..., description="用于简单任务的经济型模型")
    
    # 其他配置
    redis_url: str = "redis://localhost:6379"
    
    # ... 更多应用配置

    # 一个辅助方法，方便根据角色获取配置
    def get_llm_config_by_role(self, role: str) -> LLMConfig:
        if role == "reasoning":
            return self.reasoning_llm
        elif role == "basic":
            return self.basic_llm
        else:
            raise ValueError(f"Unknown LLM role: {role}")

# 创建一个全局单例供应用使用
settings = Settings()

# 使用示例:
# from src.core.config import settings
# planner_llm_config = settings.get_llm_config_by_role("reasoning")
# agent = PlannerAgent(llm_config=planner_llm_config.model_dump())
```

该结构允许我们在`config/default.yaml`中定义模型名称，而在`.env`文件中安全地管理API密钥，实现了配置的清晰、安全与灵活。

### 10.3. LLM 服务管理器 (LLMManager)

为了将Agent的业务逻辑与LLM的配置和实例化过程完全解耦，我们引入一个`LLMManager`。

*   **职责**: `LLMManager`是一个工厂或单例，作为系统中获取`llm_config`的唯一入口。它负责从`Settings`对象中读取原始配置，并将其转换为AutoGen Agent可以接收的、符合格式的`llm_config`字典。
*   **好处**: Agent的开发者无需关心API Key在哪里、模型名称是什么，只需向`LLMManager`请求一个特定角色的LLM配置即可，例如`manager.get_config("reasoning")`。

#### 10.3.1. 自定义与本地模型接入

本框架的核心优势之一是能够轻松接入任何兼容OpenAI API标准的模型，包括通过Ollama、vLLM等工具在本地部署的开源模型。

**接入方法**:
1.  在`config/default.yaml`（或对应的环境配置文件）中，为你的自定义模型添加一个新的角色或修改现有角色。
2.  设置`base_url`指向你的本地服务地址。
3.  设置`api_key`。对于许多本地模型（如Ollama），`api_key`可以设置为任意非空字符串（例如 "ollama"）。
4.  设置`model`为该服务所托管的模型名称。

**本地模型配置示例 (`config/development.yaml`):**
```yaml
# 覆盖默认配置，在开发环境中使用本地模型
reasoning_llm:
  model: "llama3-70b-instruct"
  api_key: "ollama" # 对于Ollama, API Key可以是任意字符串
  base_url: "http://localhost:11434/v1"

basic_llm:
  model: "qwen2-7b-instruct"
  api_key: "ollama"
  base_url: "http://localhost:11434/v1"
```

通过此配置，当Agent请求一个"reasoning"角色的LLM时，`LLMManager`会自动为其提供连接到本地Llama3模型的配置，而Agent本身的代码完全无需改动。这种方式极大地增强了系统的灵活性和可测试性。

#### 10.3.2. Agent集成示例

```python
# In src/agents/planner.py (conceptual)
from src.llms.manager import llm_manager # 假设的LLMManager单例
from autogen import AssistantAgent

class PlannerAgent(AssistantAgent):
    def __init__(self, **kwargs):
        # 从LLMManager获取专为规划和推理设计的llm_config
        reasoning_llm_config = llm_manager.get_config("reasoning")
        
        super().__init__(
            name="planner_agent",
            llm_config=reasoning_llm_config,
            # ...其他参数
        )

# 在服务启动或任务初始化时
# planner = PlannerAgent()
```
这个设计确保了Agent的核心逻辑与其使用的具体模型实现完全分离。
