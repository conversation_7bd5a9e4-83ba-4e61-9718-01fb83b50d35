"""
高德地图MCP客户端

通过SSE接口调用高德地图MCP服务，提供地理定位、路线规划、POI搜索等功能。
"""
import asyncio
import json
import uuid
from typing import Dict, Any, Optional, List
import httpx
from contextlib import asynccontextmanager
from src.core.logger import get_logger
from src.core.config import get_settings

logger = get_logger("amap_mcp_client")


class AmapMCPClient:
    """高德地图MCP客户端"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化高德MCP客户端
        
        Args:
            api_key: 高德API密钥，如果不提供则从配置中读取
        """
        self.api_key = api_key or "e8f742bdb09f99c8c6b035b7f1f04e66"
        self.base_url = "https://mcp.amap.com/sse"
        self.session: Optional[httpx.AsyncClient] = None
        self.logger = logger
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        
    async def connect(self):
        """建立连接"""
        if self.session is None:
            self.session = httpx.AsyncClient(timeout=30.0)
            self.logger.info("高德MCP客户端已连接")
            
    async def disconnect(self):
        """断开连接"""
        if self.session:
            await self.session.aclose()
            self.session = None
            self.logger.info("高德MCP客户端已断开")
            
    async def _call_mcp_api(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP API
        
        Args:
            tool_name: 工具名称
            parameters: 工具参数
            
        Returns:
            API响应结果
        """
        if not self.session:
            await self.connect()
            
        # 构建请求数据
        request_data = {
            "id": str(uuid.uuid4()),
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": parameters
            }
        }
        
        try:
            # 发送POST请求到SSE端点
            url = f"{self.base_url}?key={self.api_key}"
            response = await self.session.post(
                url,
                json=request_data,
                headers={
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                }
            )
            response.raise_for_status()
            
            result = response.json()
            self.logger.debug(f"MCP API调用成功: {tool_name}", extra={
                "tool_name": tool_name,
                "parameters": parameters,
                "response_size": len(str(result))
            })
            
            return result
            
        except Exception as e:
            self.logger.error(f"MCP API调用失败: {tool_name}", extra={
                "tool_name": tool_name,
                "parameters": parameters,
                "error": str(e)
            })
            raise
            
    # 地理编码和逆地理编码
    async def maps_geo(self, address: str, city: Optional[str] = None) -> Dict[str, Any]:
        """地址转坐标"""
        params = {"address": address}
        if city:
            params["city"] = city
        return await self._call_mcp_api("maps_geo", params)
        
    async def maps_regeocode(self, longitude: float, latitude: float) -> Dict[str, Any]:
        """坐标转地址"""
        params = {
            "location": f"{longitude},{latitude}"
        }
        return await self._call_mcp_api("maps_regeocode", params)
        
    async def maps_ip_location(self, ip: Optional[str] = None) -> Dict[str, Any]:
        """IP定位"""
        params = {}
        if ip:
            params["ip"] = ip
        return await self._call_mcp_api("maps_ip_location", params)
        
    # 路线规划
    async def maps_direction_driving(
        self, 
        origin: str, 
        destination: str, 
        waypoints: Optional[str] = None,
        strategy: int = 0
    ) -> Dict[str, Any]:
        """驾车路线规划"""
        params = {
            "origin": origin,
            "destination": destination,
            "strategy": strategy
        }
        if waypoints:
            params["waypoints"] = waypoints
        return await self._call_mcp_api("maps_direction_driving", params)
        
    async def maps_direction_walking(
        self, 
        origin: str, 
        destination: str
    ) -> Dict[str, Any]:
        """步行路线规划"""
        params = {
            "origin": origin,
            "destination": destination
        }
        return await self._call_mcp_api("maps_direction_walking", params)
        
    async def maps_direction_bicycling(
        self, 
        origin: str, 
        destination: str
    ) -> Dict[str, Any]:
        """骑行路线规划"""
        params = {
            "origin": origin,
            "destination": destination
        }
        return await self._call_mcp_api("maps_direction_bicycling", params)
        
    async def maps_direction_transit_integrated(
        self, 
        origin: str, 
        destination: str,
        city: str,
        cityd: Optional[str] = None
    ) -> Dict[str, Any]:
        """公共交通路线规划"""
        params = {
            "origin": origin,
            "destination": destination,
            "city": city
        }
        if cityd:
            params["cityd"] = cityd
        return await self._call_mcp_api("maps_direction_transit_integrated", params)
        
    # 搜索服务
    async def maps_text_search(
        self, 
        keywords: str, 
        city: Optional[str] = None,
        page: int = 1,
        offset: int = 20
    ) -> Dict[str, Any]:
        """关键字搜索"""
        params = {
            "keywords": keywords,
            "page": page,
            "offset": offset
        }
        if city:
            params["city"] = city
        return await self._call_mcp_api("maps_text_search", params)
        
    async def maps_around_search(
        self, 
        location: str, 
        keywords: str,
        radius: int = 1000,
        page: int = 1,
        offset: int = 20
    ) -> Dict[str, Any]:
        """周边搜索"""
        params = {
            "location": location,
            "keywords": keywords,
            "radius": radius,
            "page": page,
            "offset": offset
        }
        return await self._call_mcp_api("maps_around_search", params)
        
    async def maps_search_detail(self, id: str) -> Dict[str, Any]:
        """POI详情查询"""
        params = {"id": id}
        return await self._call_mcp_api("maps_search_detail", params)
        
    # 辅助服务
    async def maps_weather(self, city: str) -> Dict[str, Any]:
        """天气查询"""
        params = {"city": city}
        return await self._call_mcp_api("maps_weather", params)
        
    async def maps_distance(
        self, 
        origins: str, 
        destination: str,
        type: int = 1
    ) -> Dict[str, Any]:
        """距离测量"""
        params = {
            "origins": origins,
            "destination": destination,
            "type": type
        }
        return await self._call_mcp_api("maps_distance", params)
        
    # App唤醒服务
    async def maps_schema_personal_map(
        self, 
        pois: str,
        name: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成个人地图链接"""
        params = {"pois": pois}
        if name:
            params["name"] = name
        return await self._call_mcp_api("maps_schema_personal_map", params)
        
    async def maps_schema_navi(
        self, 
        poiname: str,
        poiid: Optional[str] = None,
        lat: Optional[float] = None,
        lon: Optional[float] = None
    ) -> Dict[str, Any]:
        """生成导航链接"""
        params = {"poiname": poiname}
        if poiid:
            params["poiid"] = poiid
        if lat and lon:
            params["lat"] = lat
            params["lon"] = lon
        return await self._call_mcp_api("maps_schema_navi", params)
        
    async def maps_schema_take_taxi(
        self, 
        dlat: float, 
        dlon: float,
        dname: str,
        slat: Optional[float] = None,
        slon: Optional[float] = None,
        sname: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成打车链接"""
        params = {
            "dlat": dlat,
            "dlon": dlon,
            "dname": dname
        }
        if slat and slon:
            params["slat"] = slat
            params["slon"] = slon
        if sname:
            params["sname"] = sname
        return await self._call_mcp_api("maps_schema_take_taxi", params)


# 全局客户端实例
_global_client: Optional[AmapMCPClient] = None


async def get_amap_client() -> AmapMCPClient:
    """获取全局高德MCP客户端实例"""
    global _global_client
    if _global_client is None:
        _global_client = AmapMCPClient()
        await _global_client.connect()
    return _global_client


@asynccontextmanager
async def amap_client():
    """高德MCP客户端上下文管理器"""
    client = AmapMCPClient()
    try:
        await client.connect()
        yield client
    finally:
        await client.disconnect()
