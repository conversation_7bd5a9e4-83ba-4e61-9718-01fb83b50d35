"""
旅行规划Agent核心实现

基于PRD需求实现的智能旅行规划Agent，支持意图理解、工具规划、并行执行等功能。
"""
import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, AsyncGenerator
import re

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, AgentState, StreamEvent,
    EventType, ThinkingCategory, ThinkingStepPayload, ToolCallPayload,
    ToolResultPayload, POIInfo, DailyPlan, TripSummary, WeatherInfo,
    Location, MapInfo, BudgetEstimation, BudgetBreakdown, UserProfile
)
from src.tools.amap_mcp_client import get_amap_client
from src.core.logger import get_logger
from src.core.llm_manager import LLMManager
from src.core.config import get_settings

logger = get_logger("travel_planner_agent")


class TravelPlannerAgent:
    """旅行规划Agent核心类"""
    
    def __init__(self):
        """初始化旅行规划Agent"""
        self.logger = logger
        self.settings = get_settings()
        self.llm_manager = LLMManager()
        
        # 初始化LLM客户端
        self.reasoning_llm = self.llm_manager.get_client("reasoning")
        self.basic_llm = self.llm_manager.get_client("basic")
        
    async def plan_travel(
        self, 
        request: TravelPlanRequest
    ) -> AsyncGenerator[StreamEvent, None]:
        """
        执行旅行规划
        
        Args:
            request: 旅行规划请求
            
        Yields:
            StreamEvent: 流式事件
        """
        trace_id = str(uuid.uuid4())
        
        # 初始化Agent状态
        state = AgentState(
            trace_id=trace_id,
            user_id=request.user_id,
            original_query=request.query,
            current_step="init"
        )
        
        try:
            # 步骤1: 意图理解和实体提取
            async for event in self._understand_intent(state):
                yield event
                
            # 步骤2: 获取用户画像
            async for event in self._get_user_profile(state):
                yield event
                
            # 步骤3: 地理定位
            async for event in self._geolocate_destination(state):
                yield event
                
            # 步骤4: 天气查询
            async for event in self._get_weather_forecast(state):
                yield event
                
            # 步骤5: POI搜索和推荐
            async for event in self._search_and_recommend_pois(state):
                yield event
                
            # 步骤6: 行程规划
            async for event in self._plan_itinerary(state):
                yield event
                
            # 步骤7: 路线规划
            async for event in self._plan_routes(state):
                yield event
                
            # 步骤8: 预算估算
            async for event in self._estimate_budget(state):
                yield event
                
            # 步骤9: 生成最终行程
            async for event in self._generate_final_itinerary(state):
                yield event
                
        except Exception as e:
            self.logger.error(f"旅行规划过程中发生错误: {str(e)}", extra={
                "trace_id": trace_id,
                "user_id": request.user_id,
                "error": str(e)
            })
            
            # 发送错误事件
            error_event = StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=trace_id,
                event_type=EventType.ERROR,
                payload={
                    "error_message": f"规划过程中发生错误: {str(e)}",
                    "error_type": type(e).__name__
                }
            )
            yield error_event
            
    async def _understand_intent(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """意图理解和实体提取"""
        state.current_step = "intent_understanding"
        
        # 发送思考开始事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_START,
            payload={"step": "意图理解和实体提取"}
        )
        
        # 构建意图理解提示词
        intent_prompt = f"""
        请分析用户的旅行规划需求，提取关键信息：
        
        用户查询：{state.original_query}
        
        请提取以下信息（如果用户没有明确提及，请标记为null）：
        1. 目的地城市
        2. 出行天数
        3. 出行时间（具体日期或相对时间）
        4. 出行人数和类型（如情侣、家庭、朋友等）
        5. 预算范围
        6. 兴趣偏好（如文化、美食、自然风光等）
        7. 特殊需求（如无障碍、亲子友好等）
        
        请以JSON格式返回提取结果。
        """
        
        try:
            # 调用推理模型进行意图理解
            response = await self.reasoning_llm.chat_completion(
                messages=[{"role": "user", "content": intent_prompt}],
                temperature=0.1
            )
            
            # 解析提取的实体
            content = response.choices[0].message.content
            
            # 尝试提取JSON
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                extracted_entities = json.loads(json_match.group())
                state.extracted_entities = extracted_entities
                
                # 发送思考步骤事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.THINKING_STEP,
                    payload=ThinkingStepPayload(
                        category=ThinkingCategory.TRAVEL_OBJECT,
                        content=f"已提取用户需求：目的地={extracted_entities.get('destination')}, "
                               f"天数={extracted_entities.get('days')}, "
                               f"偏好={extracted_entities.get('preferences')}"
                    ).model_dump()
                )
                
        except Exception as e:
            self.logger.error(f"意图理解失败: {str(e)}")
            state.extracted_entities = {"destination": None, "days": None}
            
    async def _get_user_profile(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """获取用户画像"""
        state.current_step = "user_profile"
        
        # 这里可以从数据库或缓存中获取用户画像
        # 暂时使用默认画像
        state.user_profile = UserProfile(
            user_id=state.user_id,
            preferences={},
            tags=[],
            budget_preference="中等",
            travel_style="休闲"
        )
        
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.THINKING_STEP,
            payload=ThinkingStepPayload(
                category=ThinkingCategory.OTHER,
                content="已获取用户画像信息"
            ).model_dump()
        )
        
    async def _geolocate_destination(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """地理定位"""
        state.current_step = "geolocation"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 发送工具调用事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.TOOL_CALL,
            payload=ToolCallPayload(
                tool_name="maps_geo",
                parameters={"address": destination}
            ).model_dump()
        )
        
        try:
            amap_client = await get_amap_client()
            geo_result = await amap_client.maps_geo(destination)
            
            state.tool_results["geolocation"] = geo_result
            
            # 发送工具结果事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_geo",
                    result_summary=f"成功定位{destination}",
                    success=True
                ).model_dump()
            )
            
        except Exception as e:
            self.logger.error(f"地理定位失败: {str(e)}")
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_geo",
                    result_summary=f"定位失败: {str(e)}",
                    success=False
                ).model_dump()
            )
            
    async def _get_weather_forecast(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """获取天气预报"""
        state.current_step = "weather_forecast"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 发送工具调用事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.TOOL_CALL,
            payload=ToolCallPayload(
                tool_name="maps_weather",
                parameters={"city": destination}
            ).model_dump()
        )
        
        try:
            amap_client = await get_amap_client()
            weather_result = await amap_client.maps_weather(destination)
            
            state.tool_results["weather"] = weather_result
            
            # 发送工具结果事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_RESULT,
                payload=ToolResultPayload(
                    tool_name="maps_weather",
                    result_summary=f"获取{destination}天气预报成功",
                    success=True
                ).model_dump()
            )
            
        except Exception as e:
            self.logger.error(f"天气查询失败: {str(e)}")
            
    async def _search_and_recommend_pois(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """搜索和推荐POI"""
        state.current_step = "poi_search"
        
        destination = state.extracted_entities.get("destination")
        if not destination:
            return
            
        # 根据用户偏好搜索不同类型的POI
        poi_categories = ["景点", "美食", "酒店"]
        
        for category in poi_categories:
            # 发送工具调用事件
            yield StreamEvent(
                event_id=str(uuid.uuid4()),
                trace_id=state.trace_id,
                event_type=EventType.TOOL_CALL,
                payload=ToolCallPayload(
                    tool_name="maps_text_search",
                    parameters={"keywords": category, "city": destination}
                ).model_dump()
            )
            
            try:
                amap_client = await get_amap_client()
                search_result = await amap_client.maps_text_search(
                    keywords=category,
                    city=destination,
                    offset=10
                )
                
                state.tool_results[f"poi_{category}"] = search_result
                
                # 发送工具结果事件
                yield StreamEvent(
                    event_id=str(uuid.uuid4()),
                    trace_id=state.trace_id,
                    event_type=EventType.TOOL_RESULT,
                    payload=ToolResultPayload(
                        tool_name="maps_text_search",
                        result_summary=f"找到{category}相关POI",
                        success=True
                    ).model_dump()
                )
                
            except Exception as e:
                self.logger.error(f"POI搜索失败 ({category}): {str(e)}")
                
    async def _plan_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """行程规划"""
        state.current_step = "itinerary_planning"
        
        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在规划每日行程安排"}
        )
        
        # 这里实现具体的行程规划逻辑
        # 暂时创建示例行程
        days = state.extracted_entities.get("days", 3)
        daily_plans = []
        
        for day in range(1, days + 1):
            daily_plan = DailyPlan(
                day=day,
                theme=f"第{day}天行程",
                pois=[]
            )
            daily_plans.append(daily_plan)
            
        state.tool_results["daily_plans"] = daily_plans
        
    async def _plan_routes(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """路线规划"""
        state.current_step = "route_planning"

        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在规划最优路线"}
        )

        # 获取每日行程中的POI，规划路线
        daily_plans = state.tool_results.get("daily_plans", [])

        for daily_plan in daily_plans:
            if len(daily_plan.pois) > 1:
                # 为每日行程规划路线
                pois = daily_plan.pois
                for i in range(len(pois) - 1):
                    origin_poi = pois[i]
                    dest_poi = pois[i + 1]

                    if origin_poi.location and dest_poi.location:
                        try:
                            amap_client = await get_amap_client()
                            origin = f"{origin_poi.location.longitude},{origin_poi.location.latitude}"
                            destination = f"{dest_poi.location.longitude},{dest_poi.location.latitude}"

                            # 规划步行路线
                            route_result = await amap_client.maps_direction_walking(
                                origin=origin,
                                destination=destination
                            )

                            # 存储路线信息
                            route_key = f"route_day_{daily_plan.day}_{i}"
                            state.tool_results[route_key] = route_result

                        except Exception as e:
                            self.logger.error(f"路线规划失败: {str(e)}")

        # 生成个人地图链接
        try:
            all_pois = []
            for daily_plan in daily_plans:
                for poi in daily_plan.pois:
                    if poi.location:
                        all_pois.append(f"{poi.location.longitude},{poi.location.latitude}")

            if all_pois:
                amap_client = await get_amap_client()
                pois_str = "|".join(all_pois)
                map_result = await amap_client.maps_schema_personal_map(
                    pois=pois_str,
                    name=f"{state.extracted_entities.get('destination', '旅行')}行程地图"
                )
                state.tool_results["personal_map"] = map_result

        except Exception as e:
            self.logger.error(f"生成个人地图失败: {str(e)}")

    async def _estimate_budget(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """预算估算"""
        state.current_step = "budget_estimation"

        # 发送规划步骤事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.PLANNING_STEP,
            payload={"step": "正在估算旅行预算"}
        )

        # 基于POI类型和数量估算预算
        days = state.extracted_entities.get("days", 3)
        daily_plans = state.tool_results.get("daily_plans", [])

        # 预算估算逻辑
        accommodation_cost = days * 200  # 每晚住宿200元
        food_cost = days * 150  # 每天餐饮150元
        attraction_cost = 0
        transport_cost = days * 50  # 每天交通50元

        # 根据景点数量估算门票费用
        for daily_plan in daily_plans:
            for poi in daily_plan.pois:
                if poi.category == "景点":
                    attraction_cost += 50  # 平均门票50元

        breakdown = [
            BudgetBreakdown(category="住宿", amount=accommodation_cost),
            BudgetBreakdown(category="餐饮", amount=food_cost),
            BudgetBreakdown(category="景点门票", amount=attraction_cost),
            BudgetBreakdown(category="交通", amount=transport_cost)
        ]

        total_cost = accommodation_cost + food_cost + attraction_cost + transport_cost

        budget_estimation = BudgetEstimation(
            total_min=total_cost * 0.8,
            total_max=total_cost * 1.2,
            breakdown=breakdown
        )

        state.tool_results["budget"] = budget_estimation
        
    async def _generate_final_itinerary(self, state: AgentState) -> AsyncGenerator[StreamEvent, None]:
        """生成最终行程"""
        state.current_step = "final_generation"
        
        # 构建最终行程
        destination = state.extracted_entities.get("destination", "未知目的地")
        days = state.extracted_entities.get("days", 3)
        
        summary = TripSummary(
            title=f"{destination}{days}日游",
            days=days,
            destination_city=destination,
            tags=["智能规划"],
            description="AI智能规划的旅行行程"
        )
        
        final_itinerary = TravelItinerary(
            trace_id=state.trace_id,
            user_id=state.user_id,
            status="completed",
            raw_user_query=state.original_query,
            summary=summary,
            weather_forecast=[],
            daily_plans=state.tool_results.get("daily_plans", [])
        )
        
        state.final_itinerary = final_itinerary
        
        # 发送最终行程事件
        yield StreamEvent(
            event_id=str(uuid.uuid4()),
            trace_id=state.trace_id,
            event_type=EventType.FINAL_ITINERARY,
            payload=final_itinerary.model_dump()
        )
