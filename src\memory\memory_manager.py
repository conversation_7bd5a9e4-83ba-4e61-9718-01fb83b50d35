"""
记忆管理器

实现用户记忆系统，包括个性化推荐、偏好学习、记忆存储等功能。
"""
import asyncio
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

from src.database.mongodb_client import get_mongo_client
from src.core.logger import get_logger
from src.core.llm_manager import LLMManager

logger = get_logger("memory_manager")


class MemoryManager:
    """记忆管理器"""
    
    def __init__(self):
        """初始化记忆管理器"""
        self.logger = logger
        self.llm_manager = LLMManager()
        self.basic_llm = self.llm_manager.get_client("basic")
        
    async def store_memory(
        self, 
        user_id: str, 
        memory_type: str, 
        content: str,
        context: Optional[Dict[str, Any]] = None,
        importance_score: float = 1.0,
        tags: Optional[List[str]] = None,
        related_itinerary_id: Optional[str] = None
    ) -> str:
        """
        存储记忆
        
        Args:
            user_id: 用户ID
            memory_type: 记忆类型 (preference, experience, feedback, interaction)
            content: 记忆内容
            context: 上下文信息
            importance_score: 重要性评分 (0.0-5.0)
            tags: 标签列表
            related_itinerary_id: 关联行程ID
            
        Returns:
            记忆ID
        """
        try:
            mongo_client = await get_mongo_client()
            
            memory_data = {
                "user_id": user_id,
                "memory_type": memory_type,
                "content": content,
                "context": context or {},
                "importance_score": importance_score,
                "tags": tags or [],
                "related_itinerary_id": related_itinerary_id
            }
            
            memory_id = await mongo_client.create_memory(memory_data)
            
            self.logger.info(f"记忆已存储: {memory_id}", extra={
                "user_id": user_id,
                "memory_type": memory_type,
                "importance_score": importance_score
            })
            
            return memory_id
            
        except Exception as e:
            self.logger.error(f"存储记忆失败: {str(e)}")
            raise
            
    async def get_relevant_memories(
        self, 
        user_id: str, 
        query_context: Dict[str, Any],
        memory_types: Optional[List[str]] = None,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        获取相关记忆
        
        Args:
            user_id: 用户ID
            query_context: 查询上下文
            memory_types: 记忆类型过滤
            limit: 返回数量限制
            
        Returns:
            相关记忆列表
        """
        try:
            mongo_client = await get_mongo_client()
            
            # 获取用户所有记忆
            all_memories = []
            if memory_types:
                for memory_type in memory_types:
                    memories = await mongo_client.get_user_memories(
                        user_id=user_id,
                        memory_type=memory_type,
                        limit=50
                    )
                    all_memories.extend(memories)
            else:
                all_memories = await mongo_client.get_user_memories(
                    user_id=user_id,
                    limit=100
                )
            
            if not all_memories:
                return []
            
            # 计算记忆相关性
            scored_memories = []
            for memory in all_memories:
                relevance_score = await self._calculate_relevance(memory, query_context)
                scored_memories.append((memory, relevance_score))
            
            # 按相关性排序
            scored_memories.sort(key=lambda x: x[1], reverse=True)
            
            # 返回最相关的记忆
            return [memory for memory, score in scored_memories[:limit] if score > 0.3]
            
        except Exception as e:
            self.logger.error(f"获取相关记忆失败: {str(e)}")
            return []
            
    async def _calculate_relevance(
        self, 
        memory: Dict[str, Any], 
        query_context: Dict[str, Any]
    ) -> float:
        """
        计算记忆与查询上下文的相关性
        
        Args:
            memory: 记忆数据
            query_context: 查询上下文
            
        Returns:
            相关性评分 (0.0-1.0)
        """
        try:
            # 基础相关性评分
            base_score = 0.0
            
            # 1. 时间衰减因子
            memory_age = datetime.now() - memory.get("created_at", datetime.now())
            time_decay = max(0.1, 1.0 - (memory_age.days / 365.0))  # 一年内的记忆权重较高
            
            # 2. 重要性权重
            importance_weight = min(1.0, memory.get("importance_score", 1.0) / 5.0)
            
            # 3. 访问频率权重
            access_weight = min(1.0, memory.get("access_count", 0) / 10.0)
            
            # 4. 内容相关性
            content_relevance = await self._calculate_content_relevance(
                memory.get("content", ""),
                query_context
            )
            
            # 5. 标签匹配
            tag_relevance = self._calculate_tag_relevance(
                memory.get("tags", []),
                query_context
            )
            
            # 6. 上下文匹配
            context_relevance = self._calculate_context_relevance(
                memory.get("context", {}),
                query_context
            )
            
            # 综合评分
            base_score = (
                content_relevance * 0.4 +
                tag_relevance * 0.2 +
                context_relevance * 0.2 +
                importance_weight * 0.1 +
                access_weight * 0.1
            )
            
            # 应用时间衰减
            final_score = base_score * time_decay
            
            return min(1.0, max(0.0, final_score))
            
        except Exception as e:
            self.logger.error(f"计算记忆相关性失败: {str(e)}")
            return 0.0
            
    async def _calculate_content_relevance(
        self, 
        memory_content: str, 
        query_context: Dict[str, Any]
    ) -> float:
        """计算内容相关性"""
        try:
            # 提取查询中的关键信息
            query_text = query_context.get("query", "")
            destination = query_context.get("destination", "")
            preferences = query_context.get("preferences", [])
            
            # 简单的关键词匹配
            relevance_score = 0.0
            
            # 目的地匹配
            if destination and destination.lower() in memory_content.lower():
                relevance_score += 0.5
                
            # 偏好匹配
            for pref in preferences:
                if pref.lower() in memory_content.lower():
                    relevance_score += 0.2
                    
            # 查询关键词匹配
            query_keywords = query_text.lower().split()
            memory_words = memory_content.lower().split()
            
            matching_words = set(query_keywords) & set(memory_words)
            if matching_words:
                relevance_score += len(matching_words) / len(query_keywords) * 0.3
                
            return min(1.0, relevance_score)
            
        except Exception as e:
            self.logger.error(f"计算内容相关性失败: {str(e)}")
            return 0.0
            
    def _calculate_tag_relevance(
        self, 
        memory_tags: List[str], 
        query_context: Dict[str, Any]
    ) -> float:
        """计算标签相关性"""
        try:
            query_tags = query_context.get("tags", [])
            preferences = query_context.get("preferences", [])
            
            all_query_tags = set(query_tags + preferences)
            memory_tag_set = set(memory_tags)
            
            if not all_query_tags or not memory_tag_set:
                return 0.0
                
            # 计算标签交集比例
            intersection = all_query_tags & memory_tag_set
            union = all_query_tags | memory_tag_set
            
            return len(intersection) / len(union) if union else 0.0
            
        except Exception as e:
            self.logger.error(f"计算标签相关性失败: {str(e)}")
            return 0.0
            
    def _calculate_context_relevance(
        self, 
        memory_context: Dict[str, Any], 
        query_context: Dict[str, Any]
    ) -> float:
        """计算上下文相关性"""
        try:
            relevance_score = 0.0
            
            # 比较关键上下文字段
            context_fields = ["destination", "days", "budget_range", "travel_style"]
            
            for field in context_fields:
                memory_value = memory_context.get(field)
                query_value = query_context.get(field)
                
                if memory_value and query_value:
                    if str(memory_value).lower() == str(query_value).lower():
                        relevance_score += 0.25
                        
            return relevance_score
            
        except Exception as e:
            self.logger.error(f"计算上下文相关性失败: {str(e)}")
            return 0.0
            
    async def learn_from_interaction(
        self, 
        user_id: str, 
        interaction_data: Dict[str, Any]
    ):
        """
        从用户交互中学习
        
        Args:
            user_id: 用户ID
            interaction_data: 交互数据
        """
        try:
            # 提取学习要点
            learning_points = await self._extract_learning_points(interaction_data)
            
            for point in learning_points:
                await self.store_memory(
                    user_id=user_id,
                    memory_type="interaction",
                    content=point["content"],
                    context=point["context"],
                    importance_score=point["importance"],
                    tags=point["tags"]
                )
                
            self.logger.info(f"从交互中学习了 {len(learning_points)} 个要点", extra={
                "user_id": user_id
            })
            
        except Exception as e:
            self.logger.error(f"从交互中学习失败: {str(e)}")
            
    async def _extract_learning_points(
        self, 
        interaction_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """从交互数据中提取学习要点"""
        try:
            learning_points = []
            
            # 从用户查询中提取偏好
            query = interaction_data.get("query", "")
            if query:
                # 使用LLM提取偏好信息
                preference_prompt = f"""
                分析以下用户查询，提取用户的旅行偏好和需求：
                
                查询：{query}
                
                请提取：
                1. 目的地偏好
                2. 活动类型偏好
                3. 预算倾向
                4. 时间偏好
                5. 其他特殊需求
                
                以JSON格式返回提取结果。
                """
                
                try:
                    response = await self.basic_llm.chat_completion(
                        messages=[{"role": "user", "content": preference_prompt}],
                        temperature=0.1
                    )
                    
                    # 解析LLM响应
                    content = response.choices[0].message.content
                    # 这里可以进一步解析和处理LLM的响应
                    
                    learning_points.append({
                        "content": f"用户查询偏好: {query}",
                        "context": interaction_data,
                        "importance": 1.5,
                        "tags": ["preference", "query"]
                    })
                    
                except Exception as e:
                    self.logger.error(f"LLM偏好提取失败: {str(e)}")
            
            # 从反馈中学习
            feedback = interaction_data.get("feedback")
            if feedback:
                learning_points.append({
                    "content": f"用户反馈: {feedback}",
                    "context": interaction_data,
                    "importance": 2.0,
                    "tags": ["feedback", "improvement"]
                })
            
            # 从选择行为中学习
            selected_pois = interaction_data.get("selected_pois", [])
            if selected_pois:
                for poi in selected_pois:
                    learning_points.append({
                        "content": f"用户选择了POI: {poi.get('name')} ({poi.get('category')})",
                        "context": {"poi": poi, "interaction": interaction_data},
                        "importance": 1.0,
                        "tags": ["selection", poi.get("category", "unknown")]
                    })
            
            return learning_points
            
        except Exception as e:
            self.logger.error(f"提取学习要点失败: {str(e)}")
            return []
            
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户偏好摘要
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户偏好摘要
        """
        try:
            mongo_client = await get_mongo_client()
            
            # 获取偏好相关记忆
            preference_memories = await mongo_client.get_user_memories(
                user_id=user_id,
                memory_type="preference",
                limit=50
            )
            
            # 统计偏好
            preferences = {
                "destinations": defaultdict(int),
                "activities": defaultdict(int),
                "budget_ranges": defaultdict(int),
                "travel_styles": defaultdict(int),
                "poi_categories": defaultdict(int)
            }
            
            for memory in preference_memories:
                context = memory.get("context", {})
                tags = memory.get("tags", [])
                
                # 统计目的地偏好
                destination = context.get("destination")
                if destination:
                    preferences["destinations"][destination] += memory.get("importance_score", 1.0)
                
                # 统计活动偏好
                for tag in tags:
                    if tag in ["文化", "美食", "自然", "购物", "娱乐"]:
                        preferences["activities"][tag] += memory.get("importance_score", 1.0)
                        
                # 统计预算偏好
                budget = context.get("budget_range")
                if budget:
                    preferences["budget_ranges"][budget] += memory.get("importance_score", 1.0)
            
            # 转换为排序列表
            result = {}
            for category, counts in preferences.items():
                sorted_items = sorted(counts.items(), key=lambda x: x[1], reverse=True)
                result[category] = sorted_items[:5]  # 取前5个
                
            return result
            
        except Exception as e:
            self.logger.error(f"获取用户偏好失败: {str(e)}")
            return {}
            
    async def update_memory_access(self, memory_id: str):
        """更新记忆访问记录"""
        try:
            mongo_client = await get_mongo_client()
            
            # 更新访问时间和次数
            collection = mongo_client.get_collection("memories")
            await collection.update_one(
                {"_id": memory_id},
                {
                    "$set": {"accessed_at": datetime.now()},
                    "$inc": {"access_count": 1}
                }
            )
            
        except Exception as e:
            self.logger.error(f"更新记忆访问记录失败: {str(e)}")


# 全局记忆管理器实例
_global_memory_manager: Optional[MemoryManager] = None


def get_memory_manager() -> MemoryManager:
    """获取全局记忆管理器实例"""
    global _global_memory_manager
    if _global_memory_manager is None:
        _global_memory_manager = MemoryManager()
    return _global_memory_manager
