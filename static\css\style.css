/* AutoPilot AI 旅行规划 - 样式文件 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

.container-fluid {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 8px;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.25rem;
}

.card-title {
    color: #495057;
    font-weight: 600;
}

.card-title i {
    margin-right: 8px;
    color: #007bff;
}

/* 规划面板样式 */
.planning-panel {
    padding-right: 10px;
}

.planning-panel .card {
    min-height: calc(100vh - 140px);
}

/* 结果面板样式 */
.result-panel {
    padding-left: 10px;
}

.result-panel .card {
    min-height: calc(100vh - 140px);
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* 思考过程样式 */
.thinking-steps {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.thinking-step {
    padding: 8px 12px;
    margin-bottom: 8px;
    background-color: #fff;
    border-radius: 6px;
    border-left: 4px solid #007bff;
    font-size: 0.9rem;
    animation: fadeInUp 0.3s ease;
}

.thinking-step.category-travel-object {
    border-left-color: #28a745;
}

.thinking-step.category-travel-time {
    border-left-color: #ffc107;
}

.thinking-step.category-attraction {
    border-left-color: #dc3545;
}

.thinking-step.category-food {
    border-left-color: #fd7e14;
}

.thinking-step.category-accommodation {
    border-left-color: #6f42c1;
}

/* 统计卡片样式 */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2rem;
    margin-right: 15px;
    opacity: 0.8;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 每日行程样式 */
.daily-plan {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.daily-plan-header {
    display: flex;
    justify-content-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.day-number {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}

.day-theme {
    flex: 1;
    margin-left: 15px;
}

.day-theme h5 {
    margin: 0;
    color: #495057;
}

.day-theme p {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}

/* POI卡片样式 */
.poi-card {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    position: relative;
}

.poi-card:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.poi-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.poi-info {
    flex: 1;
}

.poi-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 5px;
}

.poi-category {
    display: inline-block;
    padding: 2px 8px;
    background-color: #e9ecef;
    color: #495057;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.poi-category.attraction {
    background-color: #d4edda;
    color: #155724;
}

.poi-category.food {
    background-color: #fff3cd;
    color: #856404;
}

.poi-category.hotel {
    background-color: #d1ecf1;
    color: #0c5460;
}

.poi-actions {
    display: flex;
    gap: 5px;
}

.poi-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background-color: white;
    padding: 40px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .planning-panel,
    .result-panel {
        padding: 0;
        margin-bottom: 20px;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-icon {
        font-size: 1.5rem;
        margin-right: 10px;
    }
    
    .stat-value {
        font-size: 1.4rem;
    }
    
    .daily-plan-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .day-number {
        margin-bottom: 10px;
    }
    
    .day-theme {
        margin-left: 0;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.8rem;
}

/* 按钮组样式 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: 8px 0 0 8px;
}

.btn-group .btn:last-child {
    border-radius: 0 8px 8px 0;
}

/* 滚动条样式 */
.thinking-steps::-webkit-scrollbar {
    width: 6px;
}

.thinking-steps::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.thinking-steps::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.thinking-steps::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
