/**
 * AutoPilot AI 旅行规划 - 前端应用逻辑
 */

class TravelPlannerApp {
    constructor() {
        this.currentTraceId = null;
        this.eventSource = null;
        this.currentItinerary = null;
        this.progressSteps = [
            '意图理解',
            '地理定位', 
            '天气查询',
            'POI搜索',
            '行程规划',
            '路线规划',
            '预算估算',
            '生成结果'
        ];
        this.currentStep = 0;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupViewModes();
        this.loadUserHistory();
    }

    bindEvents() {
        // 规划表单提交
        document.getElementById('planningForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPlanning();
        });

        // 视图模式切换
        document.getElementById('viewModeList').addEventListener('click', () => {
            this.switchViewMode('list');
        });

        document.getElementById('viewModeMap').addEventListener('click', () => {
            this.switchViewMode('map');
        });

        // 行程操作按钮
        document.getElementById('saveItinerary').addEventListener('click', () => {
            this.saveItinerary();
        });

        document.getElementById('editItinerary').addEventListener('click', () => {
            this.editItinerary();
        });

        document.getElementById('shareItinerary').addEventListener('click', () => {
            this.shareItinerary();
        });

        // 导航链接
        document.querySelector('a[href="#history"]').addEventListener('click', (e) => {
            e.preventDefault();
            this.showHistory();
        });
    }

    setupViewModes() {
        // 默认显示列表视图
        this.switchViewMode('list');
    }

    async startPlanning() {
        const query = document.getElementById('userQuery').value.trim();
        const userId = document.getElementById('userId').value.trim();

        if (!query || !userId) {
            this.showAlert('请填写完整的规划信息', 'warning');
            return;
        }

        try {
            // 显示加载状态
            this.showLoading(true);
            this.showPlanningProgress(true);
            this.hideWelcomeView();

            // 重置进度
            this.currentStep = 0;
            this.updateProgress(0, '开始规划...');

            // 创建规划任务
            const response = await fetch('/api/travel/plan', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    user_id: userId
                })
            });

            if (!response.ok) {
                throw new Error('创建规划任务失败');
            }

            const result = await response.json();
            this.currentTraceId = result.trace_id;

            // 开始SSE连接
            this.startSSEConnection(query, userId);

        } catch (error) {
            console.error('规划失败:', error);
            this.showAlert('规划失败: ' + error.message, 'danger');
            this.showLoading(false);
            this.showPlanningProgress(false);
        }
    }

    startSSEConnection(query, userId) {
        const url = `/api/travel/plan/${this.currentTraceId}/stream?user_id=${encodeURIComponent(userId)}&query=${encodeURIComponent(query)}`;
        
        this.eventSource = new EventSource(url);

        this.eventSource.onopen = () => {
            console.log('SSE连接已建立');
        };

        this.eventSource.addEventListener('start', (event) => {
            const data = JSON.parse(event.data);
            console.log('规划开始:', data);
            this.updateProgress(10, '开始分析需求...');
        });

        this.eventSource.addEventListener('thinking_step', (event) => {
            const data = JSON.parse(event.data);
            this.addThinkingStep(data.payload);
            this.updateProgress(20 + this.currentStep * 10, `正在${this.progressSteps[this.currentStep] || '处理'}...`);
        });

        this.eventSource.addEventListener('tool_call', (event) => {
            const data = JSON.parse(event.data);
            this.addThinkingStep({
                category: 'other',
                content: `调用工具: ${data.payload.tool_name}`
            });
        });

        this.eventSource.addEventListener('tool_result', (event) => {
            const data = JSON.parse(event.data);
            this.addThinkingStep({
                category: 'other',
                content: `工具结果: ${data.payload.result_summary}`
            });
        });

        this.eventSource.addEventListener('planning_step', (event) => {
            const data = JSON.parse(event.data);
            this.currentStep++;
            this.updateProgress(30 + this.currentStep * 8, data.payload.step);
        });

        this.eventSource.addEventListener('final_itinerary', (event) => {
            const data = JSON.parse(event.data);
            this.currentItinerary = data.payload;
            this.displayItinerary(data.payload);
            this.updateProgress(100, '规划完成！');
            this.showLoading(false);
            this.closeSSEConnection();
        });

        this.eventSource.addEventListener('error', (event) => {
            const data = JSON.parse(event.data);
            console.error('规划错误:', data);
            this.showAlert('规划过程中发生错误: ' + data.error, 'danger');
            this.showLoading(false);
            this.closeSSEConnection();
        });

        this.eventSource.addEventListener('complete', (event) => {
            console.log('规划完成');
            this.closeSSEConnection();
        });

        this.eventSource.onerror = (error) => {
            console.error('SSE连接错误:', error);
            this.showAlert('连接中断，请重试', 'warning');
            this.showLoading(false);
            this.closeSSEConnection();
        };
    }

    closeSSEConnection() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    updateProgress(percentage, text) {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        
        progressBar.style.width = percentage + '%';
        progressText.textContent = text;
    }

    addThinkingStep(payload) {
        const thinkingSteps = document.getElementById('thinkingSteps');
        const step = document.createElement('div');
        step.className = `thinking-step category-${payload.category.toLowerCase().replace(/[^a-z]/g, '-')}`;
        step.textContent = payload.content;
        
        thinkingSteps.appendChild(step);
        thinkingSteps.scrollTop = thinkingSteps.scrollHeight;
        
        // 显示思考过程面板
        document.getElementById('thinkingProcess').style.display = 'block';
    }

    displayItinerary(itinerary) {
        // 显示行程概览
        this.showItinerarySummary(itinerary);
        
        // 显示每日行程
        this.showDailyItinerary(itinerary);
        
        // 隐藏欢迎界面
        this.hideWelcomeView();
    }

    showItinerarySummary(itinerary) {
        const summary = itinerary.summary;
        
        document.getElementById('itineraryTitle').textContent = summary.title;
        document.getElementById('itineraryDescription').textContent = summary.description || '智能AI规划的个性化旅行行程';
        document.getElementById('totalDays').textContent = summary.days;
        document.getElementById('totalPOIs').textContent = this.countTotalPOIs(itinerary.daily_plans);
        
        // 预算信息
        if (itinerary.budget_estimation) {
            const budget = itinerary.budget_estimation;
            const avgBudget = (budget.total_min + budget.total_max) / 2;
            document.getElementById('estimatedBudget').textContent = `¥${Math.round(avgBudget)}`;
        }
        
        // 天气信息
        if (itinerary.weather_forecast && itinerary.weather_forecast.length > 0) {
            const weather = itinerary.weather_forecast[0];
            document.getElementById('weatherInfo').textContent = weather.day_weather || '晴';
        }
        
        document.getElementById('itinerarySummary').style.display = 'block';
    }

    showDailyItinerary(itinerary) {
        const dailyPlans = document.getElementById('dailyPlans');
        dailyPlans.innerHTML = '';
        
        itinerary.daily_plans.forEach((plan, index) => {
            const dayElement = this.createDayElement(plan, index + 1);
            dailyPlans.appendChild(dayElement);
        });
        
        document.getElementById('dailyItinerary').style.display = 'block';
    }

    createDayElement(plan, dayNumber) {
        const dayDiv = document.createElement('div');
        dayDiv.className = 'daily-plan';
        
        dayDiv.innerHTML = `
            <div class="daily-plan-header">
                <div class="day-number">${dayNumber}</div>
                <div class="day-theme">
                    <h5>第${dayNumber}天</h5>
                    <p>${plan.theme || '精彩行程'}</p>
                </div>
            </div>
            <div class="pois-list">
                ${plan.pois.map(poi => this.createPOIElement(poi)).join('')}
            </div>
        `;
        
        return dayDiv;
    }

    createPOIElement(poi) {
        const categoryClass = poi.category.toLowerCase();
        return `
            <div class="poi-card">
                <div class="poi-header">
                    <div class="poi-info">
                        <div class="poi-name">${poi.name}</div>
                        <span class="poi-category ${categoryClass}">${poi.category}</span>
                        ${poi.description ? `<div class="poi-description">${poi.description}</div>` : ''}
                        ${poi.address ? `<div class="text-muted small"><i class="bi bi-geo-alt"></i> ${poi.address}</div>` : ''}
                        ${poi.estimated_duration_min ? `<div class="text-muted small"><i class="bi bi-clock"></i> 预计${poi.estimated_duration_min}分钟</div>` : ''}
                    </div>
                    <div class="poi-actions">
                        <button class="btn btn-outline-primary btn-sm" onclick="app.showPOIDetail('${poi.poi_instance_id}')">
                            <i class="bi bi-info-circle"></i>
                        </button>
                        ${poi.navi_url ? `<a href="${poi.navi_url}" class="btn btn-outline-success btn-sm" target="_blank"><i class="bi bi-navigation"></i></a>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    countTotalPOIs(dailyPlans) {
        return dailyPlans.reduce((total, plan) => total + plan.pois.length, 0);
    }

    switchViewMode(mode) {
        const listBtn = document.getElementById('viewModeList');
        const mapBtn = document.getElementById('viewModeMap');
        const dailyView = document.getElementById('dailyItinerary');
        const mapView = document.getElementById('mapView');
        
        if (mode === 'list') {
            listBtn.classList.add('active');
            mapBtn.classList.remove('active');
            dailyView.style.display = 'block';
            mapView.style.display = 'none';
        } else {
            mapBtn.classList.add('active');
            listBtn.classList.remove('active');
            dailyView.style.display = 'none';
            mapView.style.display = 'block';
        }
    }

    showPlanningProgress(show) {
        document.getElementById('planningProgress').style.display = show ? 'block' : 'none';
    }

    showLoading(show) {
        document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
        document.getElementById('planButton').disabled = show;
    }

    hideWelcomeView() {
        document.getElementById('welcomeView').style.display = 'none';
    }

    showAlert(message, type = 'info') {
        // 创建Bootstrap警告框
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        // 插入到页面顶部
        document.body.insertBefore(alertDiv, document.body.firstChild);
        
        // 3秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }

    async saveItinerary() {
        if (!this.currentItinerary) return;
        
        try {
            // 这里可以实现保存逻辑
            this.showAlert('行程已保存', 'success');
        } catch (error) {
            this.showAlert('保存失败: ' + error.message, 'danger');
        }
    }

    editItinerary() {
        if (!this.currentItinerary) return;
        
        // 这里可以实现编辑逻辑
        this.showAlert('编辑功能开发中...', 'info');
    }

    shareItinerary() {
        if (!this.currentItinerary) return;
        
        // 生成分享链接
        const shareUrl = `${window.location.origin}/share/${this.currentTraceId}`;
        
        if (navigator.share) {
            navigator.share({
                title: this.currentItinerary.summary.title,
                text: this.currentItinerary.summary.description,
                url: shareUrl
            });
        } else {
            // 复制到剪贴板
            navigator.clipboard.writeText(shareUrl).then(() => {
                this.showAlert('分享链接已复制到剪贴板', 'success');
            });
        }
    }

    async loadUserHistory() {
        // 这里可以实现加载用户历史行程的逻辑
    }

    showHistory() {
        // 显示历史行程模态框
        const modal = new bootstrap.Modal(document.getElementById('historyModal'));
        modal.show();
    }

    showPOIDetail(poiId) {
        // 显示POI详情
        this.showAlert('POI详情功能开发中...', 'info');
    }
}

// 初始化应用
const app = new TravelPlannerApp();
