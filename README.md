# AutoPilot AI - 智能Agent系统

一个基于Python的智能Agent系统，支持多种LLM后端，提供灵活的配置管理和强大的Agent协作能力。

## 🌟 主要特性

### 核心能力
- **统一配置管理**：基于Pydantic的类型安全配置系统
- **多LLM支持**：支持智谱AI、OpenAI等多种模型提供商
- **角色区分**：支持基础对话（basic）和复杂推理（reasoning）两种角色
- **完整监控**：结构化日志、性能监控、分布式追踪

### Agent系统
- **自研Agent框架**：轻量级、高性能的Agent实现
- **Microsoft AutoGen集成**：完全兼容AutoGen框架，支持多Agent协作
- **框架共存**：两套Agent系统可以并存，根据场景选择

### 开发体验
- **TDD开发**：完整的单元测试和集成测试覆盖
- **类型安全**：全面的类型注解和mypy检查
- **规范化**：统一的代码格式和开发规范

## 🚀 快速开始

### 1. 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd autopilotai

# 创建虚拟环境
python -m venv .venv
.\.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -e .
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
# 基础模型配置（glm-4-flash）
BASIC_LLM_MODEL=glm-4-flash
BASIC_LLM_API_KEY=your-api-key
BASIC_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4

# 推理模型配置（glm-z1-flash）
REASONING_LLM_MODEL=glm-z1-flash
REASONING_LLM_API_KEY=your-api-key
REASONING_LLM_BASE_URL=https://open.bigmodel.cn/api/paas/v4
```

### 3. 运行测试

```bash
# 运行所有测试
.\tests\scripts\test_all_agents.ps1

# 仅运行AutoGen测试
.\tests\scripts\test_all_agents.ps1 -AutoGen

# 运行单元测试
pytest tests/unit/ -v
```

## 📚 使用示例

### 基础Agent使用

```python
import asyncio
from src.agents.simple_assistant import SimpleAssistant

async def main():
    # 创建基础助手
    assistant = SimpleAssistant("智能助手", "basic")
    
    # 发送消息
    response = await assistant.send_message("你好，请介绍一下自己")
    print(response)
    
    # 创建推理助手
    reasoning_assistant = SimpleAssistant("推理助手", "reasoning")
    response = await reasoning_assistant.send_message("分析一下人工智能的发展趋势")
    print(response)

if __name__ == "__main__":
    asyncio.run(main())
```

### AutoGen集成使用

```python
import asyncio
from tests.integration.test_autogen_agent_integration import AutoGenIntegration

async def main():
    # 创建AutoGen集成实例
    integration = AutoGenIntegration()
    
    # 创建AutoGen助手
    assistant = integration.create_simple_assistant("AutoGen助手", "basic")
    
    # 进行对话
    response = await integration.simple_chat_test("帮我分析一个技术问题", "reasoning")
    print(response)

if __name__ == "__main__":
    asyncio.run(main())
```

## 🏗️ 项目架构

```
autopilotai/
├── src/                    # 主要源码
│   ├── core/              # 核心模块
│   │   ├── config.py      # 配置管理
│   │   ├── logger.py      # 日志系统
│   │   └── llm_manager.py # LLM管理器
│   └── agents/            # Agent实现
│       ├── simple_assistant.py    # 基础助手
│       └── python_expert.py       # Python专家
├── tests/                 # 测试套件
│   ├── unit/             # 单元测试
│   ├── integration/      # 集成测试
│   └── scripts/          # 测试脚本
├── config/               # 配置文件
└── doc/                  # 项目文档
```

## 🔧 支持的模型

### 智谱AI模型
- **glm-4-flash**：基础对话模型，响应快速，适合日常交互
- **glm-z1-flash**：推理模型，支持复杂思考和分析

### OpenAI模型（计划支持）
- GPT-4、GPT-3.5-turbo等（通过配置适配）

## 🧪 测试体系

### 测试覆盖
- **单元测试**：39+ 个测试用例，覆盖核心模块
- **集成测试**：端到端流程验证
- **AutoGen适配器测试**：17个Mock测试，确保集成稳定

### 测试命令
```bash
# 完整测试套件
.\tests\scripts\test_all_agents.ps1

# 指定测试类型
.\tests\scripts\test_all_agents.ps1 -UnitOnly    # 仅单元测试
.\tests\scripts\test_all_agents.ps1 -AutoGen     # 仅AutoGen测试
.\tests\scripts\test_all_agents.ps1 -Quick       # 快速测试

# 覆盖率测试
pytest tests/unit/ --cov=src --cov-report=html
```

## 📖 文档

- [快速开始指南](AUTOGEN_QUICKSTART.md) - AutoGen集成使用指南
- [测试指南](doc/测试指南.md) - 详细的测试说明
- [Agent技术规范](doc/Agent技术实现规范.md) - Agent开发规范
- [项目进度](doc/进度文档.md) - 开发进度记录

## 🛠️ 开发

### 开发环境
```bash
# 安装开发依赖
pip install -e ".[dev]"

# 代码格式化
black src/ tests/

# 类型检查
mypy src/

# 运行测试
pytest tests/unit/ -v
```

### 贡献指南
1. Fork项目
2. 创建特性分支
3. 编写测试用例
4. 确保所有测试通过
5. 提交Pull Request

## 📊 项目状态

| 模块 | 状态 | 测试覆盖率 | 说明 |
|------|------|------------|------|
| 配置管理 | ✅ 完成 | 100% | 支持多环境配置 |
| 日志系统 | ✅ 完成 | 99% | 结构化日志、分布式追踪 |
| LLM管理器 | ✅ 完成 | 84% | 支持多种LLM后端 |
| 基础Agent | ✅ 完成 | 85%+ | 对话管理、状态维护 |
| AutoGen集成 | ✅ 完成 | 95%+ | 完全兼容AutoGen框架 |

## 🔮 规划路线

### 已完成（阶段一）
- ✅ 核心框架（配置、日志、LLM管理）
- ✅ 基础Agent系统
- ✅ AutoGen框架集成
- ✅ 完整测试体系

### 进行中（阶段二）
- 🔄 工具注册表系统
- 🔄 本地工具集成（搜索、文件操作等）
- 🔄 ExecutorAgent实现

### 计划中（阶段三）
- 📋 多Agent协作工作流
- 📋 Web界面和API服务
- 📋 插件系统和生态

## 📞 支持

如有问题或建议：
1. 查看[文档](doc/)了解详细信息
2. 运行诊断测试检查环境
3. 提交Issue或联系开发团队

## 📄 许可证

[待添加许可证信息]

---

**AutoPilot AI** - 让AI Agent开发更简单、更强大