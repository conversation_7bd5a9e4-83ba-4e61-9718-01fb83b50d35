"""
旅行规划Agent相关的数据模型

定义旅行规划过程中使用的所有数据结构，包括请求、响应、状态等。
"""
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class TaskStatus(str, Enum):
    """任务状态枚举"""
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    IN_PROGRESS = "IN_PROGRESS"
    CLARIFICATION_NEEDED = "CLARIFICATION_NEEDED"


class ResponseType(str, Enum):
    """响应类型枚举"""
    ANSWER = "ANSWER"
    TASK_PLAN = "TASK_PLAN"
    USER_CLARIFICATION = "USER_CLARIFICATION"
    EXECUTION_COMMAND = "EXECUTION_COMMAND"
    STATUS_UPDATE = "STATUS_UPDATE"


class DisplayType(str, Enum):
    """显示类型枚举"""
    TEXT = "TEXT"
    CARD = "CARD"
    MAP = "MAP"
    LIST = "LIST"


class EventType(str, Enum):
    """SSE事件类型枚举"""
    THINKING_START = "thinking_start"
    THINKING_STEP = "thinking_step"
    TOOL_CALL = "tool_call"
    TOOL_RESULT = "tool_result"
    PLANNING_STEP = "planning_step"
    FINAL_ITINERARY = "final_itinerary"
    ERROR = "error"


class ThinkingCategory(str, Enum):
    """思考过程分类"""
    TRAVEL_OBJECT = "出行对象"
    TRAVEL_TIME = "出行时间"
    ATTRACTION_RECOMMENDATION = "景点推荐"
    FOOD_RECOMMENDATION = "美食推荐"
    ACCOMMODATION_RECOMMENDATION = "住宿推荐"
    OTHER = "其他"


class Location(BaseModel):
    """地理位置信息"""
    longitude: float = Field(..., description="经度")
    latitude: float = Field(..., description="纬度")
    name: Optional[str] = Field(None, description="地点名称")
    address: Optional[str] = Field(None, description="详细地址")


class POIInfo(BaseModel):
    """POI（兴趣点）信息"""
    poi_instance_id: str = Field(..., description="POI实例ID")
    poi_id: Optional[str] = Field(None, description="高德POI ID")
    name: str = Field(..., description="POI名称")
    category: str = Field(..., description="POI类别：景点、美食、酒店、购物、其他")
    image_url: Optional[str] = Field(None, description="图片URL")
    rating: Optional[float] = Field(None, description="评分")
    address: Optional[str] = Field(None, description="地址")
    location: Optional[Location] = Field(None, description="地理位置")
    opening_hours: Optional[str] = Field(None, description="营业时间")
    estimated_duration_min: Optional[int] = Field(None, description="预计游玩时长（分钟）")
    description: Optional[str] = Field(None, description="描述")
    tips: Optional[str] = Field(None, description="小贴士")
    navi_url: Optional[str] = Field(None, description="导航链接")


class DailyPlan(BaseModel):
    """每日行程计划"""
    day: int = Field(..., description="第几天")
    theme: Optional[str] = Field(None, description="当日主题")
    pois: List[POIInfo] = Field(default_factory=list, description="POI列表")


class WeatherInfo(BaseModel):
    """天气信息"""
    date: str = Field(..., description="日期")
    day_weather: str = Field(..., description="白天天气")
    night_weather: str = Field(..., description="夜间天气")
    day_temp: str = Field(..., description="白天温度")
    night_temp: str = Field(..., description="夜间温度")


class MapInfo(BaseModel):
    """地图信息"""
    center_point: Optional[Location] = Field(None, description="中心点")
    route_polyline: Optional[str] = Field(None, description="路线编码")
    personal_map_url: Optional[str] = Field(None, description="个人地图链接")


class BudgetBreakdown(BaseModel):
    """预算明细"""
    category: str = Field(..., description="类别")
    amount: float = Field(..., description="金额")


class BudgetEstimation(BaseModel):
    """预算估算"""
    total_min: Optional[float] = Field(None, description="最低预算")
    total_max: Optional[float] = Field(None, description="最高预算")
    breakdown: List[BudgetBreakdown] = Field(default_factory=list, description="预算明细")


class TripSummary(BaseModel):
    """行程摘要"""
    title: str = Field(..., description="行程标题")
    days: int = Field(..., description="天数")
    destination_city: str = Field(..., description="目的地城市")
    tags: List[str] = Field(default_factory=list, description="标签")
    cover_image_url: Optional[str] = Field(None, description="封面图片URL")
    description: Optional[str] = Field(None, description="描述")


class TravelPlanRequest(BaseModel):
    """旅行规划请求"""
    user_id: str = Field(..., description="用户ID")
    query: str = Field(..., description="用户查询")
    session_id: Optional[str] = Field(None, description="会话ID")


class TravelItinerary(BaseModel):
    """完整旅行行程"""
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    status: str = Field(..., description="状态")
    raw_user_query: str = Field(..., description="原始用户查询")
    summary: TripSummary = Field(..., description="行程摘要")
    weather_forecast: List[WeatherInfo] = Field(default_factory=list, description="天气预报")
    map_info: Optional[MapInfo] = Field(None, description="地图信息")
    daily_plans: List[DailyPlan] = Field(default_factory=list, description="每日计划")
    budget_estimation: Optional[BudgetEstimation] = Field(None, description="预算估算")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class StreamEvent(BaseModel):
    """SSE流事件"""
    event_id: str = Field(..., description="事件ID")
    trace_id: str = Field(..., description="追踪ID")
    event_type: EventType = Field(..., description="事件类型")
    payload: Dict[str, Any] = Field(..., description="事件负载")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class ThinkingStepPayload(BaseModel):
    """思考步骤负载"""
    category: ThinkingCategory = Field(..., description="思考类别")
    content: str = Field(..., description="思考内容")


class ToolCallPayload(BaseModel):
    """工具调用负载"""
    tool_name: str = Field(..., description="工具名称")
    parameters: Dict[str, Any] = Field(..., description="工具参数")


class ToolResultPayload(BaseModel):
    """工具结果负载"""
    tool_name: str = Field(..., description="工具名称")
    result_summary: str = Field(..., description="结果摘要")
    success: bool = Field(..., description="是否成功")


class DisplayContent(BaseModel):
    """显示内容"""
    title: Optional[str] = Field(None, description="标题")
    text: str = Field(..., description="文本内容")
    items: Optional[List[Dict[str, Any]]] = Field(None, description="列表项")


class VPAActions(BaseModel):
    """虚拟助手动作"""
    expression: Optional[str] = Field(None, description="表情")
    action: Optional[str] = Field(None, description="动作")


class DisplayInfo(BaseModel):
    """显示信息"""
    type: DisplayType = Field(..., description="显示类型")
    content: DisplayContent = Field(..., description="显示内容")
    vpa_actions: Optional[VPAActions] = Field(None, description="虚拟助手动作")


class ExecutionTrace(BaseModel):
    """执行轨迹"""
    main_agent: str = Field(..., description="主要Agent")
    steps: List[str] = Field(..., description="执行步骤")
    duration_ms: int = Field(..., description="执行时长（毫秒）")


class ErrorDetails(BaseModel):
    """错误详情"""
    code: Optional[str] = Field(None, description="错误代码")
    message: Optional[str] = Field(None, description="错误消息")


class UnifiedResponse(BaseModel):
    """统一响应格式"""
    task_id: str = Field(..., description="任务ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    status: TaskStatus = Field(..., description="任务状态")
    response_type: ResponseType = Field(..., description="响应类型")
    payload: Dict[str, Any] = Field(..., description="响应负载")
    display: Optional[DisplayInfo] = Field(None, description="显示信息")
    execution_trace: Optional[ExecutionTrace] = Field(None, description="执行轨迹")
    error_details: Optional[ErrorDetails] = Field(None, description="错误详情")


class UserProfile(BaseModel):
    """用户画像"""
    user_id: str = Field(..., description="用户ID")
    preferences: Dict[str, Any] = Field(default_factory=dict, description="偏好设置")
    tags: List[str] = Field(default_factory=list, description="用户标签")
    budget_preference: Optional[str] = Field(None, description="预算偏好")
    travel_style: Optional[str] = Field(None, description="旅行风格")


class AgentState(BaseModel):
    """Agent状态"""
    trace_id: str = Field(..., description="追踪ID")
    user_id: str = Field(..., description="用户ID")
    original_query: str = Field(..., description="原始查询")
    user_profile: Optional[UserProfile] = Field(None, description="用户画像")
    extracted_entities: Dict[str, Any] = Field(default_factory=dict, description="提取的实体")
    tool_results: Dict[str, Any] = Field(default_factory=dict, description="工具结果")
    current_step: str = Field(default="init", description="当前步骤")
    events: List[StreamEvent] = Field(default_factory=list, description="事件列表")
    final_itinerary: Optional[TravelItinerary] = Field(None, description="最终行程")
