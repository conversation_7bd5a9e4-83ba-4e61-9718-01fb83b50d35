"""
LLM服务管理器

统一的LLM客户端管理，支持多种LLM提供商、连接池、错误重试等功能。
遵循OpenAI API标准，便于集成各种兼容的模型服务。
"""
import uuid
import asyncio
import aiohttp
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from contextlib import asynccontextmanager

from src.core.config import LLMConfig, get_settings
from src.core.logger import get_logger, get_performance_logger, get_trace_logger


class LLMError(Exception):
    """LLM服务相关错误"""
    pass


@dataclass
class ChatMessage:
    """聊天消息数据结构"""
    role: str  # system, user, assistant
    content: str


class LLMClient:
    """
    LLM客户端
    
    支持OpenAI兼容的API调用，自动处理请求/响应日志、错误重试等。
    """
    
    def __init__(self, config: LLMConfig):
        self.config = config
        self.model = config.model
        self.api_key = config.api_key
        self.base_url = config.base_url
        self.api_version = config.api_version
        
        # 日志记录器
        self.logger = get_logger(f"llm_client.{self.model}")
        self.perf_logger = get_performance_logger(f"llm_perf.{self.model}")
        self.trace_logger = get_trace_logger(f"llm_trace.{self.model}")
        
        # HTTP客户端会话（延迟创建）
        self._session: Optional[aiohttp.ClientSession] = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """获取HTTP客户端会话（单例模式）"""
        if self._session is None or self._session.closed:
            timeout = aiohttp.ClientTimeout(total=60)  # 60秒超时
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            self._session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers
            )
        return self._session
    
    async def close(self):
        """关闭HTTP客户端会话"""
        if self._session and not self._session.closed:
            await self._session.close()
    
    def _build_chat_url(self) -> str:
        """构建聊天API的URL"""
        base = self.base_url.rstrip('/')
        return f"{base}/chat/completions"
    
    def _prepare_messages(
        self, 
        user_message: str, 
        system_message: Optional[str] = None,
        history: Optional[List[ChatMessage]] = None
    ) -> List[Dict[str, str]]:
        """准备聊天消息"""
        messages = []
        
        # 添加系统消息
        if system_message:
            messages.append({
                "role": "system",
                "content": system_message
            })
        
        # 添加历史消息
        if history:
            for msg in history:
                messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
        
        # 添加用户消息
        messages.append({
            "role": "user",
            "content": user_message
        })
        
        return messages
    
    async def chat(
        self,
        message: str,
        system_message: Optional[str] = None,
        history: Optional[List[ChatMessage]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        发送聊天请求
        
        Args:
            message: 用户消息
            system_message: 系统消息
            history: 聊天历史
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            包含回复内容和元数据的字典
        """
        request_id = str(uuid.uuid4())
        
        # 开始追踪
        trace_id = self.trace_logger.start_trace(f"llm_chat_{self.model}")
        
        try:
            with self.perf_logger.measure(f"chat_{self.model}"):
                # 准备请求
                messages = self._prepare_messages(message, system_message, history)
                
                request_data = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": temperature,
                }
                
                if max_tokens:
                    request_data["max_tokens"] = max_tokens
                
                # 记录请求日志
                self.logger.info(
                    "Sending LLM request",
                    request_id=request_id,
                    model=self.model,
                    message_count=len(messages),
                    temperature=temperature
                )
                
                self.trace_logger.info(
                    "LLM request prepared",
                    step="request_preparation",
                    message_length=len(message),
                    has_system=bool(system_message),
                    history_length=len(history) if history else 0
                )
                
                # 发送请求
                session = await self._get_session()
                url = self._build_chat_url()
                
                async with session.post(url, json=request_data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        self.logger.error(
                            "LLM request failed",
                            request_id=request_id,
                            status_code=response.status,
                            error=error_text
                        )
                        raise LLMError(f"LLM API error {response.status}: {error_text}")
                    
                    result = await response.json()
                
                # 提取响应内容
                if not result.get("choices"):
                    raise LLMError("No choices in LLM response")
                
                content = result["choices"][0]["message"]["content"]
                usage = result.get("usage", {})
                
                # 记录响应日志
                self.logger.info(
                    "LLM request completed",
                    request_id=request_id,
                    response_length=len(content),
                    total_tokens=usage.get("total_tokens", 0)
                )
                
                self.trace_logger.info(
                    "LLM response received",
                    step="response_processing",
                    content_length=len(content),
                    total_tokens=usage.get("total_tokens", 0)
                )
                
                return {
                    "content": content,
                    "usage": usage,
                    "request_id": request_id,
                    "model": self.model
                }
        
        except Exception as e:
            self.logger.error(
                "LLM request error",
                request_id=request_id,
                error=str(e),
                exc_info=True
            )
            self.trace_logger.error(
                "LLM request failed",
                step="error_handling",
                error=str(e)
            )
            raise
        
        finally:
            # 结束追踪
            self.trace_logger.end_trace()


class LLMPool:
    """
    LLM客户端连接池
    
    管理多个LLM客户端实例，支持并发请求和负载均衡。
    """
    
    def __init__(self, config: LLMConfig, pool_size: int = 3):
        self.config = config
        self.pool_size = pool_size
        self.logger = get_logger(f"llm_pool.{config.model}")
        
        # 创建客户端池
        self._clients = [LLMClient(config) for _ in range(pool_size)]
        self._available = asyncio.Queue()
        self._in_use = set()
        
        # 初始化可用队列
        for client in self._clients:
            self._available.put_nowait(client)
    
    async def acquire(self) -> LLMClient:
        """获取一个可用的客户端"""
        client = await self._available.get()
        self._in_use.add(client)
        
        self.logger.debug(
            "Client acquired from pool",
            available_count=self._available.qsize(),
            in_use_count=len(self._in_use)
        )
        
        return client
    
    async def release(self, client: LLMClient):
        """释放客户端回到池中"""
        if client in self._in_use:
            self._in_use.remove(client)
            await self._available.put(client)
            
            self.logger.debug(
                "Client released to pool",
                available_count=self._available.qsize(),
                in_use_count=len(self._in_use)
            )
    
    @asynccontextmanager
    async def get_client(self):
        """上下文管理器方式获取客户端"""
        client = await self.acquire()
        try:
            yield client
        finally:
            await self.release(client)
    
    async def close_all(self):
        """关闭所有客户端"""
        for client in self._clients:
            await client.close()


class LLMManager:
    """
    LLM管理器
    
    统一管理不同角色的LLM客户端，提供简化的聊天接口。
    """
    
    def __init__(self):
        self.logger = get_logger("llm_manager")
        self._clients: Dict[str, LLMClient] = {}
        self._pools: Dict[str, LLMPool] = {}
        self._settings = get_settings()
    
    def _get_config_by_role(self, role: str) -> LLMConfig:
        """根据角色获取LLM配置"""
        if role == "reasoning":
            return self._settings.reasoning_llm
        elif role == "basic":
            return self._settings.basic_llm
        else:
            raise ValueError(f"Unknown LLM role: {role}")
    
    def get_client(self, role: str) -> LLMClient:
        """获取指定角色的LLM客户端"""
        if role not in self._clients:
            config = self._get_config_by_role(role)
            self._clients[role] = LLMClient(config)
            
            self.logger.info(
                "Created LLM client",
                role=role,
                model=config.model
            )
        
        return self._clients[role]
    
    def get_pool(self, role: str, pool_size: int = 3) -> LLMPool:
        """获取指定角色的LLM连接池"""
        pool_key = f"{role}_{pool_size}"
        
        if pool_key not in self._pools:
            config = self._get_config_by_role(role)
            self._pools[pool_key] = LLMPool(config, pool_size)
            
            self.logger.info(
                "Created LLM pool",
                role=role,
                model=config.model,
                pool_size=pool_size
            )
        
        return self._pools[pool_key]
    
    async def chat(
        self,
        message: str,
        role: str = "basic",
        **kwargs
    ) -> Dict[str, Any]:
        """
        便捷的聊天接口
        
        Args:
            message: 用户消息
            role: LLM角色 (reasoning/basic)
            **kwargs: 其他聊天参数
            
        Returns:
            LLM响应
        """
        client = self.get_client(role)
        return await client.chat(message, **kwargs)
    
    async def close_all(self):
        """关闭所有客户端和连接池"""
        for client in self._clients.values():
            await client.close()
        
        for pool in self._pools.values():
            await pool.close_all()
        
        self.logger.info("All LLM clients closed")


# 全局管理器实例
_default_manager: Optional[LLMManager] = None


def get_default_manager() -> LLMManager:
    """获取默认LLM管理器（单例模式）"""
    global _default_manager
    if _default_manager is None:
        _default_manager = LLMManager()
    return _default_manager


async def quick_chat(
    message: str,
    role: str = "basic",
    **kwargs
) -> Dict[str, Any]:
    """
    快速聊天函数
    
    Args:
        message: 用户消息
        role: LLM角色
        **kwargs: 其他参数
        
    Returns:
        LLM响应
    """
    manager = get_default_manager()
    return await manager.chat(message, role=role, **kwargs)


# 使用示例：
# 
# # 1. 直接使用客户端
# from src.core.config import LLMConfig
# config = LLMConfig(model="glm-z1-flash", api_key="...", base_url="...")
# client = LLMClient(config)
# response = await client.chat("你好")
#
# # 2. 使用管理器
# manager = LLMManager()
# response = await manager.chat("复杂问题", role="reasoning")
# 
# # 3. 快速聊天
# response = await quick_chat("简单问题")
#
# # 4. 使用连接池
# pool = manager.get_pool("basic", pool_size=5)
# async with pool.get_client() as client:
#     response = await client.chat("并发请求") 