# AutoPilot AI - 数据流转与记忆体系

本文档详细描述了 AutoPilot AI 系统中，一次完整的用户交互（从请求发起到任务结束）所涉及的核心数据流转、状态管理以及与分层记忆系统的交互机制。该设计旨在确保高并发下的实时性能、数据一致性与长期记忆的有效沉淀。

## 核心原则

- **状态与持久化分离**：使用 Redis 作为高性能的"实时作战室"，处理任务执行过程中的高频读写；使用 MySQL 和 MongoDB 作为"永久档案馆"，负责任务的最终归档和长期记忆的持久化。
- **任务ID贯穿始终**：一个全局唯一的 `task_id` 是串联所有系统（Redis, MySQL, MongoDB）和日志的关键，确保了端到端的可追溯性。
- **异步归档**：核心交互流程不应被数据库的写入延迟阻塞。数据的持久化（归档）作为任务结束后的异步步骤，与用户响应解耦。

## 端到端数据流转图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Vehicle as 车端
    participant Gateway as API网关/中枢
    participant Redis as L0/L1: 即时/短期记忆<br/>(作战室)
    participant Engine as AutoPilot AI引擎<br/>(Agent集群)
    participant MySQL as L2: 结构化记忆<br/>(用户档案)
    participant MongoDB as L2: 非结构化记忆<br/>(交互日志)

    User->>Vehicle: 发起复杂请求 (如 "规划周末亲子游")
    Vehicle->>+Gateway: POST /v1/tasks (封装请求和车端上下文)
    
    Gateway->>Gateway: 1. 生成全局 task_id
    
    par "三方占坑 (Initiation)"
        Gateway->>+MySQL: INSERT `ai_planning_sessions` (task_id, status: 'PROCESSING')
        Gateway->>+MongoDB: insertOne `ai_interaction_logs` (骨架文档: task_id, status: 'PROCESSING')
        Gateway->>+Redis: HSET `task:{task_id}` (设置初始状态, TTL 30min)
    end
    
    MySQL-->>-Gateway: OK
    MongoDB-->>-Gateway: OK
    Redis-->>-Gateway: OK
    Gateway-->>-Vehicle: HTTP 202 (返回 task_id 和 SSE stream_url)
    
    Vehicle->>Gateway: GET /v1/stream/{task_id} (建立SSE连接)
    
    Note over Gateway, Engine: 网关触发AI引擎执行任务
    Gateway->>Engine: run_task(task_id)

    loop "AI引擎执行 (Execution)"
        Note over Engine, Redis: L0: 实时状态写入Redis
        Engine->>Redis: HSET `task:{task_id}` (更新Agent执行步骤、中间结果、进度)
        
        Note over Redis, Vehicle: SSE推送实时进度
        Redis-->>Gateway: (Pub/Sub or Polling)
        Gateway-->>Vehicle: event: status_update (推送Redis中的最新进度)
        
        Note over Engine, MySQL: L2: 检索长期记忆
        Engine->>MySQL: SELECT * FROM `user_memories` WHERE user_id = ?
        Engine->>MySQL: SELECT * FROM `user_summaries` WHERE user_id = ?
        MySQL-->>Engine: 返回用户画像和记忆片段
        
        Engine->>Engine: 融合记忆，执行规划...
    end

    Note over Engine, Gateway: 任务完成
    Engine->>Gateway: report_success(task_id, final_result)

    par "数据归档 (Archiving)"
        Gateway->>+Redis: HGETALL `task:{task_id}` (获取完整执行记录)
        Redis-->>-Gateway: 返回所有中间数据
        
        Gateway->>+MongoDB: updateOne `ai_interaction_logs` (用Redis数据填充完整日志)
        MongoDB-->>-Gateway: OK

        Gateway->>+MySQL: UPDATE `ai_planning_sessions` (status: 'SUCCESS')
        MySQL-->>-Gateway: OK

        Gateway->>Redis: DEL `task:{task_id}`
    end

    Note over Engine, MySQL: 记忆沉淀 (Learning)
    Engine->>Engine: 调用记忆生成/评估Agent
    Engine->>MySQL: INSERT/UPDATE `user_memories` (新增或更新用户记忆)
    Engine->>MySQL: UPDATE `user_summaries` (更新用户画像摘要)

    Gateway-->>Vehicle: event: task_result (推送最终结果)
    Gateway-->>Vehicle: event: close (关闭SSE连接)

```

## 数据流转详解

### 阶段一：任务初始化 (三方占坑)

当用户发起一个需要 AutoPilot AI 处理的复杂任务时，系统首先确保该任务的"身份"被牢固确立，即使后续处理失败，也能追踪到记录。

1.  **生成唯一ID**: API网关或中枢系统为本次交互生成一个全局唯一的 `task_id` (例如: `UUID`)。
2.  **MySQL 占坑**: 在 `ai_planning_sessions` 表中插入一条新记录。此记录的核心是 `task_id` 和一个初始状态，如 `'PROCESSING'`。这提供了一个可靠的、事务性的任务总览。
3.  **MongoDB 占坑**: 在 `ai_interaction_logs` 集合中插入一个"骨架"文档。该文档仅包含 `task_id`、`user_id` 和初始状态，为未来的详细日志归档预留了位置。
4.  **Redis 创建"作战室"**: 在 Redis 中创建一个 **Hash**，其 `key` 为 `task:{task_id}`。这个 Hash 将作为本次任务执行期间所有动态数据的"实时白板"。同时，为其设置一个合理的过期时间（如30分钟），作为超时自动清理的保险措施。

完成这三步后，任务的持久化记录和实时处理空间均已备好。系统可以安全地向客户端返回 `task_id`，并开始真正的智能处理。

### 阶段二：任务执行 (实时读写 Redis)

此阶段是 AI 引擎的核心工作区域，所有操作都围绕高性能的 Redis 进行，以最大程度降低延迟，并向前端提供流畅的实时反馈。

1.  **记忆检索 (L2 -> L0/L1)**:
    -   **规划类Agent** (如 `TripPlanningAgent`) 启动后，首先会通过 **记忆检索Agent** 从 **MySQL** 的 `user_memories` 和 `user_summaries` 表中拉取与当前用户和任务相关的长期记忆（用户画像、偏好、习惯等）。
    -   这些检索到的长期记忆被加载到当前任务在 Redis 的 Hash 中，成为会话期间的 **L1 短期记忆**。

2.  **实时状态更新 (L0)**:
    -   AI引擎中的各个Agent（规划、搜索、分析等）在执行过程中，会产生大量的中间状态和日志。
    -   这些信息，包括对业务有意义的步骤 (`business_steps_log`)、模型的原始思考链 (`raw_model_trace`)、当前的进度百分比等，都会被实时地 `HSET` 到 Redis 的任务Hash中。
    -   这构成了 **L0 即时记忆**，它的生命周期仅限于当前任务的单个执行步骤。

3.  **实时进度反馈**:
    -   一个独立的SSE（Server-Sent Events）服务负责监控 Redis 中任务 Hash 的变化。
    -   一旦检测到内容更新，它会立刻将最新的进度信息通过流式连接推送到车端，供UI展示。
    -   **此阶段完全不涉及对 MySQL 和 MongoDB 的任何读写**，确保了核心规划链路的最高性能。

### 阶段三：任务结束 (归档与记忆沉淀)

当 AI 引擎成功完成任务并生成最终结果后，系统进入收尾阶段，将"作战室"的成果进行永久封存和学习。

1.  **数据归档 (Archiving)**:
    -   **从Redis读取**: 任务协调器执行 `HGETALL`，从 Redis 中一次性取出任务 Hash 的所有数据，这包含了从开始到结束的完整执行记录。
    -   **更新MongoDB**: 执行一次 `updateOne` 操作，用从Redis获取到的完整数据填充在第一阶段创建的"骨架"文档。同时，将文档状态更新为 `'SUCCESS'` 或 `'FAILED'`。至此，一份详尽的、可供分析和调试的"黑匣子"日志已永久保存在 `ai_interaction_logs` 中。
    -   **更新MySQL**: 执行 `UPDATE` 操作，更新 `ai_planning_sessions` 表中的对应任务状态，完成任务状态的闭环。
    -   **清理Redis**: 执行 `DEL` 命令，删除对应的任务 Hash，释放内存资源。

2.  **记忆沉淀 (Learning)**:
    -   **记忆生成Agent** 会分析本次交互的完整日志（特别是用户的输入和最终选择）。
    -   如果识别出新的、有价值的用户偏好或事实，**记忆评估Agent** 会对其进行打分。
    -   对于高价值的记忆，系统会将其 `INSERT` 或 `UPDATE` 到 **MySQL** 的 `user_memories` 表中，形成新的长期记忆。
    -   同时，可能会触发对 `user_summaries` 表中用户画像摘要的更新。

这个闭环流程确保了系统在提供高性能实时体验的同时，也构建了一个能够持续学习和进化的、安全可靠的长期记忆系统。 