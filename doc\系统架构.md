# AutoPilot AI (领航AI) 系统架构 (融合 AutoGen)

本文档结合了 "AutoPilot AI (领航AI)" 的核心业务流程与 Microsoft AutoGen 框架的特性，提出一个更具体、更侧重于多 Agent 协同实现的系统架构。

## 核心理念

借鉴 `旅游搭子.md` 中的双链路设计：
1.  **链路一 (快速响应模式):** 对于简单、即时性的用户请求，系统应能快速规划并执行少量工具调用，直接给出回应。这类似于 AutoGen 中的单个或少数几个 Agent 通过 Function Calling 与工具直接交互。
2.  **链路二 (复杂任务协同模式):** 对于复杂的用户请求（如多日行程规划），系统应启动一个由多个专门化 AutoGen Agent 组成的团队进行协同处理。每个 Agent 负责任务的一部分，通过对话和共享状态来达成最终目标。

全局的 `AgentState` (或 AutoGen 中的共享上下文/消息历史，可结合外部存储如 Redis) 依然是基础，用于存储用户请求、环境上下文、中间结果、Agent 间的对话历史等。

## 技术选型提示

*   **LLM 核心:** 选用支持强大 Function Calling (Tool Calling) 的大语言模型 (如 OpenAI GPT 系列)。
*   **Agent 框架:** 主要采用 Microsoft AutoGen (`microsoft/autogen`)。
*   **Agent 协作编排:** 对于复杂的多 Agent 协作，除了 AutoGen 内置的 `GroupChatManager`，可以考虑采用更高级的图结构化编排 (Graph-based orchestration, 例如用户提及的 `GraphFlow` 概念或自定义实现)，以实现更灵活的并行执行、条件分支和依赖管理。
*   **短期记忆/状态存储/进度跟踪:** 强烈建议使用外部高速缓存服务，如 **Redis**，用于存储 Agent 间的共享状态、短期记忆、中间结果、任务队列，**以及详细的任务调度进度和各环节状态，以便向车端等外部系统提供实时反馈**。
*   **工具/API:** 地图服务 (如高德、百度地图 API)、天气服务 API、知识图谱/搜索引擎 (如 Tavily AI)、酒店/机票预订 API、车辆控制 API (如果涉及车内场景)。
*   **消息队列/事件总线 (可选):** 对于非常复杂的异步任务或需要解耦的 Agent 通信，可以考虑引入 RabbitMQ, Kafka 等，Redis 的 Pub/Sub 也可以满足部分需求。
*   **数据库/长期存储:** 用于存储用户偏好、历史行程、Agent 配置、以及多层次的长期记忆。推荐组合方案：
    *   **MySQL :** 存储结构化的用户画像、核心偏好设置、已保存地点、行程元数据、系统配置等。
    *   **MongoDB:** 存储详细的用户对话历史、AI生成的复杂内容（如用户画像摘要、行程计划）、用户自由文本反馈、外部知识的原始文本块等。
    *   **向量数据库 (Vector Database - 如 Pinecone, Weaviate, Milvus, ChromaDB):** 存储用户对话片段的语义嵌入、用户画像摘要的嵌入、外部知识（用于RAG）的嵌入，以支持高效的语义相似性搜索和记忆检索。

## Redis 的作用与数据存储详解

在当前架构中，引入 Redis 主要是为了解决多 Agent 协作，特别是并行执行和复杂流程中，**状态共享、短期记忆、高效数据交换以及任务进度跟踪**的问题。

### Redis 的核心作用：

1.  **充当高速的短期记忆/工作记忆 (Short-Term / Working Memory):**
    *   当多个 Agent (尤其是并行执行的 Agent 或在图编排中按序执行的 Agent) 需要共享信息或传递中间结果时，Redis 提供了一个快速读写的位置。这比仅通过 Agent 间的直接消息传递要高效得多。
2.  **促进 Agent 间的解耦通信和数据交换:**
    *   一个 Agent 完成其子任务后，可以将结果存入 Redis。其他依赖此结果的 Agent 可以从 Redis 中拉取数据，而无需直接等待或与前一个 Agent 紧密耦合。这对于图编排引擎管理下的并行和条件执行流程尤为重要。
3.  **全局/共享上下文的快速访问:**
    *   像用户偏好、车辆实时位置等需要被多个 Agent 频繁读取的上下文信息，可以存储在 Redis 中，以提供低延迟的访问。
4.  **任务调度进度跟踪与反馈:**
    *   记录长链路任务的执行环节和当前状态（如"规划中"、"正在搜索酒店"、"等待用户确认"等），为外部系统（如车端界面）提供实时的任务进度反馈，提升用户体验。
5.  **任务/消息队列 (轻量级):**
    *   虽然文档中提到了 Kafka/RabbitMQ 作为可选的消息队列，但对于一些简单的异步任务分发或 Agent 间的通知，Redis 的列表结构也可以作为轻量级的队列使用。
6.  **分布式锁/同步机制 (高级场景):**
    *   在非常复杂的并行操作中，如果多个 Agent 可能同时修改共享资源，Redis 可以提供分布式锁机制来保证操作的原子性或顺序性。
7.  **长期记忆检索结果 (短期缓存/传递):**
    *   当 Agent (如 `MAIN_PLANNER_ROUTER` 或链路二中的专业 Agent) 决定需要访问长期记忆时，从 MySQL、MongoDB 或向量数据库中检索到的相关信息（如用户历史偏好文本、相关对话片段、知识块），在传递给 LLM 进行最终处理前，可以暂时存储在 Redis 中，方便作为上下文注入到 Prompt。这避免了将大量文本直接通过 Agent 消息传递。

### Redis 中存储的具体数据示例 (根据架构图和说明)：

1.  **上下文信息 (`CTX_..._AGENT` -> `CTX_AGGREGATOR` -> `MAIN_PLANNER_ROUTER`):**
    *   用户偏好 (来自 `CTX_USER_PREF_AGENT`)。
    *   车辆实时位置 (来自 `CTX_LOC_AGENT`)。
    *   当前时间 (来自 `CTX_TIME_AGENT`)。
    *   `CTX_AGGREGATOR` 汇总后的、准备给主规划器使用的完整上下文快照 (即 `AgentState` 的一部分或全部)。

2.  **Agent 的中间输出/成果 (尤其在链路二的图编排中):**
    *   `AGENT_ITINERARY_PLANNER` 生成的初步景点列表或路线规划。
    *   `AGENT_ACCOM_DINING` 推荐的住宿和餐饮选项。
    *   `AGENT_BUDGET_TRANSPORT` 计算的预算和交通方案。
    *   `AGENT_RESEARCHER` 搜索到的相关背景资料或用户评价。
    *   这些中间结果由 `GRAPH_ORCHESTRATOR` 读取，或直接被图中的下一个依赖此数据的 Agent (如 `AGENT_CONSOLIDATOR`) 读取。

3.  **工具执行结果 (暂存):**
    *   当 `TOOL_EXECUTOR` 执行工具后，其原始输出可以暂时存入 Redis，供调用该工具的 Agent (如 `SIMPLE_TASK_AGENT` 或链路二中的专业 Agent) 进一步处理和理解。

4.  **任务状态、参数与进度 (由图编排引擎管理并用于反馈):**
    *   `GRAPH_ORCHESTRATOR` 在调度图中不同 Agent 节点时，会将任务参数、当前整体任务ID、**当前执行到的具体环节描述** (例如，"正在规划第一天行程"、"正在搜索上海地区评分最高的亲子酒店")、**各环节/Agent的开始时间、预计结束时间（如果可能）、当前状态**（如等待输入、正在处理、已完成、遇到错误）等信息存入 Redis。这些信息不仅用于内部跟踪，也**可被外部接口查询以向车端提供实时进度展示**。

5.  **用户澄清/输入 (`USER_PROXY_FOR_COMPLEX`):**
    *   当复杂任务需要人工介入时，`USER_PROXY_FOR_COMPLEX` 获取到的用户输入或确认信息，可以先存入 Redis，然后由 `GRAPH_ORCHESTRATOR` 或其他等待此输入的 Agent 读取。

6.  **最终的复杂任务结果 (在生成响应前):**
    *   `AGENT_CONSOLIDATOR` (或 `GRAPH_ORCHESTRATOR` 自身) 整合出的最终详细行程计划，在传递给 `RESPONSE_GENERATOR` 之前，可以完整地存储在 Redis 中。

总结来说，Redis 在这个架构中扮演了一个"中央数据交换枢纽"、"高速缓存"和"任务进度跟踪器"的角色，使得 Agent 之间的协作更加灵活、高效，并且能够支持对外的实时状态反馈，尤其是在需要并行处理和管理复杂依赖关系的场景下。它帮助解耦了 Agent，使得它们可以独立工作，并通过共享的存储来交换信息和进度。

## 详细架构图 (基于 AutoGen 理念)

```mermaid
graph TD
    %% === 用户输入与初始处理 ===
    A["用户请求 (User Request)\n(语音/文本/触控)"] --> INPUT_ADAPTER["输入适配器 (Input Adapter)\n(语音转文本, 格式化)"]
    INPUT_ADAPTER --> CTX_ACQUISITION_MANAGER["上下文获取协调器 (Context Acquisition Manager)\n(AutoGen Agent or GroupChatManager)"]

    subgraph "并行获取与处理上下文 (Parallel Context Acquisition & Processing)\n(可由图编排引擎驱动)"
        direction LR
        CTX_LOC_AGENT["车辆位置 Agent (Vehicle Location Agent)\n(Tool: GPS API)"]
        CTX_TIME_AGENT["当前时间 Agent (Current Time Agent)\n(Tool: System Time)"]
        CTX_VEH_AGENT["车辆状态 Agent (Vehicle Status Agent)\n(Tool: 车载API)"]
        CTX_USER_PREF_AGENT["用户偏好 Agent (User Preference Agent)\n(Tools: DB/Redis, MySQL, MongoDB, VectorDB for profile - L1/部分L2预取)"]
    end
    CTX_ACQUISITION_MANAGER -.-> CTX_LOC_AGENT & CTX_TIME_AGENT & CTX_VEH_AGENT & CTX_USER_PREF_AGENT
    CTX_LOC_AGENT & CTX_TIME_AGENT & CTX_VEH_AGENT & CTX_USER_PREF_AGENT --> CTX_AGGREGATOR["上下文聚合与状态更新 Agent (Context Aggregator & State Update Agent)\n(更新 AgentState 至 Redis/共享上下文, 含L0/L1记忆)"]

    %% === 长期记忆存储 ===
    subgraph "长期记忆存储 (Long-Term Memory Stores - L2)"
        direction LR
        DB_MYSQL["MySQL/PostgreSQL\n(结构化用户画像, 核心偏好等)"]
        DB_MONGO["MongoDB\n(详细对话历史, AI生成内容, 用户反馈等)"]
        DB_VECTOR["向量数据库 (Vector DB)\n(语义嵌入, 对话/知识RAG)"]
    end

    %% === 主规划与分发 (含长期记忆访问决策) ===
    CTX_AGGREGATOR --> MAIN_PLANNER_ROUTER["主任务规划与路由 Agent (Main Task Planner & Router Agent)\n(基于L0/L1上下文, 决策是否访问L2长期记忆)"]

    MAIN_PLANNER_ROUTER -- "若需L2: 调用检索工具\n(If L2 needed: Call Retrieval Tool)" --> LTM_RETRIEVAL_TOOL["长期记忆检索工具/Agent\n(LTM Retrieval Tool/Agent - Orchestrates L2 access)"]
    LTM_RETRIEVAL_TOOL -- "访问结构化数据 (Access Structured Data)" --> DB_MYSQL
    LTM_RETRIEVAL_TOOL -- "访问非结构化数据 (Access Unstructured Data)" --> DB_MONGO
    LTM_RETRIEVAL_TOOL -- "进行语义搜索 (Perform Semantic Search)" --> DB_VECTOR
    
    LTM_RETRIEVAL_TOOL -- "L2记忆结果 (存入Redis供后续使用)\n(L2 Memory Results to Redis for subsequent use)" --> REDIS_CACHE[("Redis 高速缓存\n(Short-term Memory, State, L1 & L2 Cache, Progress)")]
    REDIS_CACHE -. "L2结果可被Planner读取 (L2 results readable by Planner)" .-> MAIN_PLANNER_ROUTER

    MAIN_PLANNER_ROUTER -- "识别为简单请求 (可能已用L2增强)\n(Simple Request, possibly L2-enhanced from Redis)" --> SIMPLE_TASK_AGENT["简单任务执行 Agent (Simple Task Execution Agent)\n(AutoGen AssistantAgent with Tools, uses context from Redis)"]
    MAIN_PLANNER_ROUTER -- "识别为复杂请求 (可能已用L2增强)\n(Complex Request, possibly L2-enhanced from Redis)" --> COMPLEX_TASK_GRAPH_ORCHESTRATOR_INIT["复杂任务图编排引擎初始化 (Complex Task Graph Orchestrator Init)"]

    %% === 链路一：简单任务处理 (Path 1: Simple Task Processing) ===
    subgraph "链路一：简单任务处理 (Simple Task Processing)"
        direction TB
        SIMPLE_TASK_AGENT -- "Function Calling (tool_calls using context from Redis)" --> TOOL_EXECUTOR["工具执行器 (Tool Executor)\n(AutoGen ToolExecutor or custom)"]
        subgraph "原子能力工具集 (Atomic Capability Toolset) (共享)"
            direction LR
            TOOL_MAP["地图服务工具 (Map Service Tool)"]
            TOOL_WEATHER["天气服务工具 (Weather Service Tool)"]
            TOOL_KNOWLEDGE["知识查询工具 (Knowledge Query Tool)"]
            TOOL_TAVILY["Tavily AI 搜索工具 (Tavily AI Search Tool)"]
            TOOL_VEHICLE["车辆控制工具 (Vehicle Control Tool)"]
            TOOL_NEWS_API["新闻API工具 (News API Tool)"]
            TOOL_MUSIC_API["音乐API工具 (Music API Tool)"]
            TOOL_PODCAST_API["播客API工具 (Podcast API Tool)"]
            TOOL_CALENDAR_API["日历API工具 (Calendar API Tool)"]
            TOOL_CONTENT_GENERATOR_LLM["内容生成LLM工具 (Content Generator LLM Tool)"]
        end
        TOOL_EXECUTOR -. "调用 (Invoke)" .-> TOOL_MAP & TOOL_WEATHER & TOOL_KNOWLEDGE & TOOL_TAVILY & TOOL_VEHICLE & TOOL_NEWS_API & TOOL_MUSIC_API & TOOL_PODCAST_API & TOOL_CALENDAR_API & TOOL_CONTENT_GENERATOR_LLM
        TOOL_MAP & TOOL_WEATHER & TOOL_KNOWLEDGE & TOOL_TAVILY & TOOL_VEHICLE & TOOL_NEWS_API & TOOL_MUSIC_API & TOOL_PODCAST_API & TOOL_CALENDAR_API & TOOL_CONTENT_GENERATOR_LLM --> TOOL_EXECUTOR
        TOOL_EXECUTOR -- "工具结果 (Tool Results)\n(可暂存Redis)" --> REDIS_CACHE
        REDIS_CACHE -. "工具结果供Agent使用 (Tool results for Agent)" .-> SIMPLE_TASK_AGENT
        SIMPLE_TASK_AGENT -- "处理后结果 (Processed Result from Redis context)" --> RESPONSE_GENERATOR["响应生成 Agent (Response Generator Agent)\n(AutoGen AssistantAgent, uses context from Redis)"]
    end

    %% === 链路二：复杂任务处理 (Path 2: Complex Task Processing) ===
    COMPLEX_TASK_GRAPH_ORCHESTRATOR_INIT --> GRAPH_ORCHESTRATOR["图编排引擎 (Graph Orchestrator)\n(管理Agent执行图, 支持并行/条件分支, 读写Redis获取增强上下文和存取进度)"]

    subgraph "链路二：多 Agent 协同 (Multi-Agent Collaboration)\n(由图编排引擎管理, Agent状态/中间结果/进度通过Redis共享, 含L0/L1/L2记忆)"
        direction TB
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_ITINERARY_PLANNER["行程规划 Agent (Itinerary Planning Agent)\n(Node in Graph, uses context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_ACCOM_DINING["住宿餐饮推荐 Agent (Accommodation & Dining Recommendation Agent)\n(Node in Graph, uses context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_BUDGET_TRANSPORT["预算交通 Agent (Budget & Transport Agent)\n(Node in Graph, uses context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_RESEARCHER["通用研究员 Agent (General Researcher Agent)\n(Node in Graph, uses context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_CONSOLIDATOR["行程整合与优化 Agent (Itinerary Consolidation & Optimization Agent)\n(Node in Graph, uses context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_CONVERSATIONAL_COMPANION["聊天陪伴 Agent (Conversational Companion Agent)\n(Node in Graph, uses Long-term Memory context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_MEDIA_RECOMMENDER_PLAYER["媒体推荐与播放 Agent (Media Recommender & Player Agent)\n(Node in Graph, uses Long-term Memory context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_NEWS_PRESENTER["新闻播报 Agent (News Presenter Agent)\n(Node in Graph, uses Long-term Memory context from Redis)"]
        GRAPH_ORCHESTRATOR -- "调度/触发 (Schedule/Trigger with context from Redis)" --> AGENT_SCHEDULER_ASSISTANT["日程助理 Agent (Scheduler Assistant Agent)\n(Node in Graph, uses Long-term Memory context from Redis & Calendar API)"]
        GRAPH_ORCHESTRATOR -- "请求人工介入 (Request Human Intervention)" --> USER_PROXY_FOR_COMPLEX["用户代理 Agent (User Proxy Agent for Complex Tasks)\n(AutoGen UserProxyAgent)
(Node for Human-in-Loop)"]
        
        USER_PROXY_FOR_COMPLEX -- "用户输入/确认 (User Input/Confirmation)\n(写入Redis/返回Orchestrator)" --> REDIS_CACHE
        REDIS_CACHE -. "用户输入供Orchestrator使用 (User input for Orchestrator)" .-> GRAPH_ORCHESTRATOR

        AGENT_ITINERARY_PLANNER -- "调用工具 (Invoke Tool)" --> TOOL_EXECUTOR_COMPLEX["共享工具执行器 (Shared Tool Executor, uses context from Redis)"]
        AGENT_ACCOM_DINING -- "调用工具 (Invoke Tool)" --> TOOL_EXECUTOR_COMPLEX
        AGENT_RESEARCHER -- "调用工具 (Invoke Tool)" --> TOOL_EXECUTOR_COMPLEX
        AGENT_CONVERSATIONAL_COMPANION -- "调用工具 (Invoke Tool, e.g., Content Generator)" --> TOOL_EXECUTOR_COMPLEX
        AGENT_MEDIA_RECOMMENDER_PLAYER -- "调用工具 (Invoke Tool, e.g., Music API, Podcast API)" --> TOOL_EXECUTOR_COMPLEX
        AGENT_NEWS_PRESENTER -- "调用工具 (Invoke Tool, e.g., News API, Tavily)" --> TOOL_EXECUTOR_COMPLEX
        AGENT_SCHEDULER_ASSISTANT -- "调用工具 (Invoke Tool, e.g., Calendar API)" --> TOOL_EXECUTOR_COMPLEX
        TOOL_EXECUTOR_COMPLEX -.-> TOOL_MAP & TOOL_WEATHER & TOOL_KNOWLEDGE & TOOL_TAVILY & TOOL_NEWS_API & TOOL_MUSIC_API & TOOL_PODCAST_API & TOOL_CALENDAR_API & TOOL_CONTENT_GENERATOR_LLM

        AGENT_ITINERARY_PLANNER -- "产出/信息 (写入Redis) (Output/Information to Redis)" --> REDIS_CACHE
        AGENT_ACCOM_DINING -- "产出/信息 (写入Redis) (Output/Information to Redis)" --> REDIS_CACHE
        AGENT_BUDGET_TRANSPORT -- "产出/信息 (写入Redis) (Output/Information to Redis)" --> REDIS_CACHE
        AGENT_RESEARCHER -- "产出/信息 (写入Redis) (Output/Information to Redis)" --> REDIS_CACHE
        AGENT_CONVERSATIONAL_COMPANION -- "对话内容/状态 (写入Redis, 更新长期记忆) (Dialogue Content/State to Redis, updates LTM)" --> REDIS_CACHE
        AGENT_MEDIA_RECOMMENDER_PLAYER -- "推荐列表/播放状态 (写入Redis, 更新长期记忆) (Recommendation List/Playback State to Redis, updates LTM)" --> REDIS_CACHE
        AGENT_NEWS_PRESENTER -- "新闻摘要/播报状态 (写入Redis) (News Summary/Presentation State to Redis)" --> REDIS_CACHE
        AGENT_SCHEDULER_ASSISTANT -- "日程条目/提醒状态 (写入Redis, 更新长期记忆) (Calendar Entry/Reminder State to Redis, updates LTM)" --> REDIS_CACHE
        
        AGENT_CONSOLIDATOR -- "整合后的计划/结果 (写入Redis) (Consolidated Plan/Result to Redis)" --> REDIS_CACHE 
    end
    TOOL_EXECUTOR_COMPLEX -- "工具结果 (写入Redis) (Tool results to Redis)" --> REDIS_CACHE
    REDIS_CACHE -. "工具结果供Agent使用 (Tool results for Agents)" .-> AGENT_ITINERARY_PLANNER & AGENT_ACCOM_DINING & AGENT_RESEARCHER & AGENT_CONVERSATIONAL_COMPANION & AGENT_MEDIA_RECOMMENDER_PLAYER & AGENT_NEWS_PRESENTER & AGENT_SCHEDULER_ASSISTANT


    REDIS_CACHE -. "最终复杂任务结果供响应生成器 (Final complex task result for Response Gen.)" .-> RESPONSE_GENERATOR
    REDIS_CACHE -.-> PROGRESS_FEEDBACK_INTERFACE["任务进度接口 (Task Progress Interface)\n(读取Redis实时进度和状态)"]
    PROGRESS_FEEDBACK_INTERFACE --> USER_INTERFACE

    %% === 响应生成与输出 ===
    RESPONSE_GENERATOR --> OUTPUT_ADAPTER["输出适配器 (Output Adapter)\n(文本转语音, UI渲染)"]
    OUTPUT_ADAPTER --> USER_INTERFACE["用户界面 (User Interface) (车端 Car Display)\n(显示最终结果和实时进度)"]

```

## 组件说明与 AutoGen 结合点

1.  **用户输入与初始处理 (`A` -> `CTX_AGGREGATOR`):**
    *   **`INPUT_ADAPTER`**: 负责将用户的原始输入（语音、文本、触控事件）转换为统一的文本格式。
    *   **`CTX_ACQUISITION_MANAGER`**:
        *   **AutoGen 实现**: 可以是一个 `AssistantAgent` 或一个简单的 `GroupChatManager`，或者由更高级的图编排引擎统一调度。
        *   职责: 并行或串行地触发专门的上下文获取 Agent。获取的上下文信息可以存储到 Redis 中供后续 Agent 使用。
    *   **上下文获取 Agents (`CTX_LOC_AGENT`, `CTX_TIME_AGENT`, etc.)**:
        *   **AutoGen 实现**: 每个都是一个 `AssistantAgent`，配置了特定的工具 (e.g., `FunctionTool` 包装的 API 调用) 来获取单一类型的上下文信息。例如, `CTX_LOC_AGENT` 使用 GPS API 工具。
    *   **`CTX_AGGREGATOR`**:
        *   **AutoGen 实现**: 一个 `AssistantAgent`。
        *   职责: 汇总所有上下文信息（可能从 Redis 读取），更新到一个全局可访问的 `AgentState` 对象或作为初始消息传递给后续 Agent。结果也可能写回 Redis。

2.  **主任务规划与路由 (`CTX_AGGREGATOR` -> `MAIN_PLANNER_ROUTER`):**
    *   **`MAIN_PLANNER_ROUTER`**:
        *   **AutoGen 实现**: 可以是一个配置了特定系统消息和 Function Calling schema 的 `AssistantAgent`，用于判断任务类型；或者是一个 `UserProxyAgent`，其 `human_input_mode` 设置为 `NEVER`，并通过代码逻辑判断后，将任务委派给简单任务 Agent 或复杂任务的图编排引擎。
        *   职责: 分析用户请求和上下文（可能从 Redis 读取），判断是简单请求、复杂任务请求，还是陪伴类请求。
            *   简单请求: 直接传递给 `SIMPLE_TASK_AGENT`。
            *   复杂请求: 初始化并启动 `COMPLEX_TASK_GRAPH_ORCHESTRATOR_INIT`，即复杂任务的图编排流程。
            *   **陪伴类请求**: 根据请求的性质和复杂度，决定将其路由到链路一的 `SIMPLE_TASK_AGENT`（用于快速、简单的陪伴指令，如"讲个笑话"），或启动链路二的图编排，激活一个或多个专门的陪伴类 Agent 进行处理。例如，"有声自由说"、"新闻时事"、"心理医生"、"深夜鸡汤"等意图，在识别后会路由给相应的专业 Agent。
        *   **决策访问长期记忆 (分层与选择性调用策略)**: `MAIN_PLANNER_ROUTER` 的核心职责之一，是高效地判断当前请求（无论是任务型还是陪伴型）是否需要以及如何利用记忆，以平衡智能化和响应速度。
            *   **L0 (当前请求信息) & L1 (短期记忆 - Redis)**: 优先利用用户当前请求中的明确信息 (L0)。同时，并行或预先从 Redis (L1) 加载当前会话上下文或用户最近的明确偏好（如最近几分钟内表达的"不吃辣"或"预算200以内"）。这些信息会作为初始上下文注入 LLM。
            *   **L2 (长期记忆 - MySQL/MongoDB/向量数据库) 的智能决策**: LLM 分析初始上下文后，判断是否需要访问长期记忆。触发 L2 检索的条件可能包括：
                *   用户明确提及历史信息（例如，"我上次说过..."）。
                *   当前请求的关键信息缺失，而历史记录中可能存在相关补充（如价格偏好、特定不吃的食物）。
                *   系统需要进行深度个性化推荐或复杂对话（如特定角色的聊天）。
            *   **调用长期记忆检索工具/Agent**: 如果决定需要访问 L2，`MAIN_PLANNER_ROUTER` 会规划一个调用"长期记忆检索工具"的步骤（该工具可能是一个专门的 Agent 或一组函数）。此工具负责：
                *   根据用户请求和上下文（尤其是触发检索的关键信息，如"不喜欢太贵的"）生成高效的查询（可能包括文本到向量的转换）。
                *   并行从 **向量数据库** (搜索语义相关的历史对话片段、用户画像摘要嵌入等)、**MySQL** (查询结构化的用户画像、核心偏好设置等) 和 **MongoDB** (查询详细的用户对话历史、AI生成的复杂内容、用户反馈等) 中检索信息。
                *   对检索结果进行汇总、筛选和排序，提取最相关的少数记忆片段，并可将其暂存到 Redis 以便传递。
            *   **再次规划或参数增强**: `MAIN_PLANNER_ROUTER` (或图编排中的后续 Agent) 接收从长期记忆中检索到的信息（例如，历史对话显示用户曾说"人均不要超过300元"）。基于此，它可以更精确地设置后续工具调用的参数（例如，为 `TOOL_MAP` 的 POI 搜索加入价格限制），或调整任务执行计划。
            *   **如果不需要 L2 记忆**: 则基于 L0 和 L1 的信息直接规划后续步骤。
            *   此整个决策和规划过程，目标是让 LLM 专注于决策"是否检索"、"检索什么"，而实际的数据库查询和数据处理由经过优化的代码高效执行。
            *   **优化考虑**: 
                *   为减少用户等待，对于可能耗时较长的 L2 检索，系统可考虑提供即时反馈（例如，"好的，正在查找您喜欢的XX，并回忆一下您之前的偏好..."）。
                *   对于不确定的记忆，可设计澄清机制（例如，"您之前是说预算在300元左右对吗？"）。
                *   **高级优化 (可选)**: 对于非常活跃的用户，可在空闲时或会话开始时，基于用户画像异步地预取一些高度相关的长期记忆片段到 Redis 中，供后续更快速的访问，但这会增加系统复杂性。
            *   此决策过程会充分利用从 Redis 中获取的，可能已包含初步长期记忆检索结果的上下文信息。

3.  **链路一：简单任务处理 (`SIMPLE_TASK_AGENT` -> `RESPONSE_GENERATOR`):**
    *   **`SIMPLE_TASK_AGENT`**:
        *   **AutoGen 实现**: 一个 `AssistantAgent`。
        *   职责: 接收简单任务或简单的陪伴指令，通过 LLM 的 Function Calling 决定需要调用的工具。中间状态或工具结果可暂存 Redis。
        *   它会与 `TOOL_EXECUTOR` 交互，获取工具执行结果，并进行初步处理。
        *   **扩展的工具调用能力**: 它需要能够调用与陪伴场景相关的工具，例如：`TOOL_KNOWLEDGE` / `TOOL_TAVILY` (用于知识问答、新闻)，`TOOL_MEDIA_CONTROL` (封装了 `TOOL_VEHICLE` 中媒体播放部分，或独立的音乐/视频平台API调用工具)，一个新的 `TOOL_CONTENT_GENERATION` (例如，调用一个专门生成笑话、故事或简单问候语的LLM模块)。
    *   **`TOOL_EXECUTOR`**:
        *   **AutoGen 实现**: AutoGen 提供了工具使用的内置支持。`AssistantAgent` 可以直接被赋予 `FunctionTool` 实例。当 LLM 生成工具调用时，AutoGen 会处理执行并返回结果。也可以自定义一个工具执行器 Agent。
        *   职责: 执行 `SIMPLE_TASK_AGENT` 或复杂任务子 Agent 请求的原子能力工具。它需要能够执行新增的与陪伴类功能相关的原子能力工具（如调用新闻API、音乐平台API、内容生成模块等）。
    *   **原子能力工具集 (`TOOL_MAP`, `TOOL_WEATHER`, etc.)**:
        *   **AutoGen 实现**: 每个工具都是一个 Python 函数，通过 `FunctionTool` 包装后，赋予相关的 Agent。
        *   **新增陪伴场景工具**:
            *   `TOOL_NEWS_API`: 专门用于获取新闻。
            *   `TOOL_MUSIC_API`: 接入音乐平台。
            *   `TOOL_PODCAST_API`: 接入播客平台。
            *   `TOOL_CALENDAR_API`: 接入日历服务。
            *   `TOOL_CONTENT_GENERATOR_LLM`: 一个专门用于生成特定类型内容（如笑话、故事、安慰性话语）的 LLM 调用封装，其 Prompt 可能与核心任务型 LLM 不同。
    *   **`RESPONSE_GENERATOR`**:
        *   **AutoGen 实现**: 一个 `AssistantAgent`，其系统消息侧重于将结构化数据或初步处理结果（可能从 Redis 读取）转换成用户友好的自然语言。
        *   职责: 接收来自 `SIMPLE_TASK_AGENT` (或链路二的 `GRAPH_ORCHESTRATOR`) 的最终结果，生成最终的用户响应。
        *   **上下文感知响应**: 它会利用所有相关的上下文信息，包括从短期或长期记忆中回忆起的偏好，以及各工具的执行结果，来生成更具个性化、情境化和信息量的回复。例如，在推荐餐馆时，可以明确提及："考虑到您之前提到过不喜欢太贵的，我为您找到了这些评价不错的日本料理，人均消费都在300元以内..."

4.  **链路二：复杂任务处理 (`COMPLEX_TASK_GRAPH_ORCHESTRATOR_INIT` -> `GRAPH_ORCHESTRATOR` -> `RESPONSE_GENERATOR`):**
    *   **`COMPLEX_TASK_GRAPH_ORCHESTRATOR_INIT`**: 这是一个逻辑步骤，负责根据 `MAIN_PLANNER_ROUTER` 的决策，创建和配置图编排引擎 (`GRAPH_ORCHESTRATOR`) 及相关的 Agent 执行图定义。
    *   **`GRAPH_ORCHESTRATOR` (图编排引擎)**:
        *   **AutoGen 实现**: 这可以是一个自定义的协调器，它管理一个预定义的或动态生成的 Agent 执行图（例如，基于有向无环图 DAG）。它可以利用 AutoGen 的 Agent 作为图中的执行节点。它可以决定 Agent 的执行顺序、并行性、条件分支，并管理 Agent 之间的数据流（通常通过 Redis）。
        *   职责:
            *   接收来自 `MAIN_PLANNER_ROUTER` 的复杂任务描述和初始数据（可能存储在 Redis 中）。
            *   根据任务图的定义，调度和触发图中的各个专业 Agent。
            *   管理 Agent 间的依赖关系和数据传递（例如，一个 Agent 的输出写入 Redis，下一个 Agent 从 Redis 读取）。
            *   处理并行执行的 Agent 分支和同步点。
            *   **将关键执行节点的状态、当前环节描述和整体进度信息写入 Redis，供车端等外部系统进行实时反馈展示。**
            *   当图中节点需要人工输入时，激活 `USER_PROXY_FOR_COMPLEX`。
            *   汇总最终结果（可能从 Redis 中整合）。
    *   **专业 Agents (`AGENT_ITINERARY_PLANNER`, `AGENT_ACCOM_DINING`, etc.)**:
        *   **AutoGen 实现**: 每个都是一个 `AssistantAgent`，作为图中的一个可执行节点。它们具有明确定义的角色和工具。
        *   它们从图编排引擎接收任务和输入数据（可能通过 Redis 获取，其中可能包含从长期记忆检索的数据），执行任务，并将输出（包括自身执行状态的更新）写回 Redis，供图编排引擎或图中的其他 Agent 使用。
    *   **新增或调整的陪伴类专业 Agents**:
        *   `AGENT_CONVERSATIONAL_COMPANION` (聊天陪伴Agent):
            *   **AutoGen 实现**: 一个 `AssistantAgent`，其 Prompt 专门为开放式闲聊、情感支持、角色扮演（如"神秘主播"、"心理医生"——需非常谨慎并声明非专业身份）而设计。
            *   **长期记忆利用**: 它会重度依赖从向量数据库中检索到的与当前话题相关的历史对话片段，以及从 MySQL/MongoDB 获取的用户画像和偏好，以实现更个性化和有深度的对话。它可能调用 `TOOL_CONTENT_GENERATOR_LLM`。
        *   `AGENT_MEDIA_RECOMMENDER_PLAYER` (媒体推荐与播放Agent):
            *   **AutoGen 实现**: 一个 `AssistantAgent`。
            *   **职责**: 负责根据用户偏好（来自长期记忆）、当前情境、历史播放记录（来自长期记忆）推荐音乐、播客、视频，并调用 `TOOL_MUSIC_API`, `TOOL_PODCAST_API` (或整合的 `TOOL_MEDIA_CONTROL`) 进行播放。
            *   **长期记忆利用**: 在推荐前，会查询 MySQL 中的音乐类型偏好标签，并可能从 MongoDB/向量库中检索用户最近常听的歌曲或表达过喜好的艺术家。
        *   `AGENT_NEWS_PRESENTER` (新闻播报Agent):
            *   **AutoGen 实现**: 一个 `AssistantAgent`。
            *   **职责**: 专门负责获取（通过 `TOOL_TAVILY` 或 `TOOL_NEWS_API` 工具）、总结和播报新闻。
            *   **长期记忆利用**: 它会利用长期记忆中的用户新闻偏好领域。
        *   `AGENT_SCHEDULER_ASSISTANT` (日程助理Agent):
            *   **AutoGen 实现**: 一个 `AssistantAgent`。
            *   **职责**: 负责处理与日程、提醒相关的请求，调用 `TOOL_CALENDAR_API` 工具。
            *   **长期记忆利用**: 它会利用长期记忆中用户的重要日期或习惯。
    *   **`USER_PROXY_FOR_COMPLEX`**:
        *   **AutoGen 实现**: 一个 `AssistantAgent`，其 `human_input_mode` 设置为 `NEVER`，并通过代码逻辑判断后，将任务委派给复杂任务的图编排流程。
        *   职责: 当复杂任务需要人工介入时，获取用户输入或确认信息，并存入 Redis。
