"""
LLM服务管理器的单元测试

测试 src.core.llm_manager 模块的功能。
"""
import os
import pytest
from unittest.mock import patch, MagicMock, AsyncMock
import asyncio


class TestLLMClient:
    """测试LLM客户端"""
    
    def test_llm_client_creation_with_config(self):
        """测试使用配置创建LLM客户端"""
        from src.core.llm_manager import LLMClient
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        client = LLMClient(config)
        
        assert client.config == config
        assert client.model == "glm-z1-flash"
        assert client.api_key == "test_key"
        assert client.base_url == "https://open.bigmodel.cn/api/paas/v4/"
    
    @pytest.mark.asyncio
    async def test_llm_client_simple_chat(self):
        """测试简单聊天功能"""
        from src.core.llm_manager import LLMClient
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        client = LLMClient(config)
        
        # Mock HTTP请求
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "choices": [{
                    "message": {"content": "测试回复"}
                }],
                "usage": {"total_tokens": 100}
            }
            mock_response.status = 200
            mock_post.return_value.__aenter__.return_value = mock_response
            
            response = await client.chat("你好")
            
            assert response["content"] == "测试回复"
            assert response["usage"]["total_tokens"] == 100
            assert "request_id" in response
    
    @pytest.mark.asyncio 
    async def test_llm_client_with_system_message(self):
        """测试带系统消息的聊天"""
        from src.core.llm_manager import LLMClient
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        client = LLMClient(config)
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.json.return_value = {
                "choices": [{"message": {"content": "专业回复"}}],
                "usage": {"total_tokens": 120}
            }
            mock_response.status = 200
            mock_post.return_value.__aenter__.return_value = mock_response
            
            response = await client.chat(
                "用户问题",
                system_message="你是一个专业的AI助手"
            )
            
            assert response["content"] == "专业回复"
            
            # 验证请求体包含system消息
            args, kwargs = mock_post.call_args
            request_data = kwargs['json']
            messages = request_data['messages']
            
            assert len(messages) == 2
            assert messages[0]['role'] == 'system'
            assert messages[0]['content'] == '你是一个专业的AI助手'
            assert messages[1]['role'] == 'user'
            assert messages[1]['content'] == '用户问题'
    
    @pytest.mark.asyncio
    async def test_llm_client_error_handling(self):
        """测试错误处理"""
        from src.core.llm_manager import LLMClient, LLMError
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        client = LLMClient(config)
        
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_response = AsyncMock()
            mock_response.status = 500
            mock_response.text.return_value = "Internal Server Error"
            mock_post.return_value.__aenter__.return_value = mock_response
            
            with pytest.raises(LLMError) as exc_info:
                await client.chat("测试")
            
            assert "500" in str(exc_info.value)


class TestLLMManager:
    """测试LLM管理器"""
    
    def test_llm_manager_creation(self):
        """测试LLM管理器创建"""
        from src.core.llm_manager import LLMManager
        
        manager = LLMManager()
        assert manager is not None
    
    def test_get_client_by_role(self):
        """测试按角色获取客户端"""
        from src.core.llm_manager import LLMManager
        from src.core.config import settings
        
        manager = LLMManager()
        
        # 需要先设置环境变量以避免配置错误
        import os
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "test_key",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "test_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            
            reasoning_client = manager.get_client("reasoning")
            basic_client = manager.get_client("basic")
            
            assert reasoning_client is not None
            assert basic_client is not None
            assert reasoning_client.model == "glm-z1-flash"
            assert basic_client.model == "glm-4-flash"
    
    def test_get_client_invalid_role(self):
        """测试无效角色抛出错误"""
        from src.core.llm_manager import LLMManager
        
        manager = LLMManager()
        
        with pytest.raises(ValueError, match="Unknown LLM role: invalid"):
            manager.get_client("invalid")
    
    @pytest.mark.asyncio
    async def test_manager_chat_with_role(self):
        """测试管理器按角色聊天"""
        from src.core.llm_manager import LLMManager
        
        manager = LLMManager()
        
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "test_key",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "test_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            with patch('aiohttp.ClientSession.post') as mock_post:
                mock_response = AsyncMock()
                mock_response.json.return_value = {
                    "choices": [{"message": {"content": "思考后的回答"}}],
                    "usage": {"total_tokens": 150}
                }
                mock_response.status = 200
                mock_post.return_value.__aenter__.return_value = mock_response
                
                response = await manager.chat("复杂问题", role="reasoning")
                
                assert response["content"] == "思考后的回答"


class TestLLMPool:
    """测试LLM连接池"""
    
    def test_pool_creation(self):
        """测试连接池创建"""
        from src.core.llm_manager import LLMPool
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        pool = LLMPool(config, pool_size=3)
        
        assert pool.config == config
        assert pool.pool_size == 3
        assert len(pool._clients) == 3
    
    @pytest.mark.asyncio
    async def test_pool_acquire_release(self):
        """测试连接池获取和释放客户端"""
        from src.core.llm_manager import LLMPool
        from src.core.config import LLMConfig
        
        config = LLMConfig(
            model="glm-z1-flash",
            api_key="test_key",
            base_url="https://open.bigmodel.cn/api/paas/v4/"
        )
        
        pool = LLMPool(config, pool_size=2)
        
        # 获取客户端
        client1 = await pool.acquire()
        client2 = await pool.acquire()
        
        assert client1 is not None
        assert client2 is not None
        assert client1 is not client2
        
        # 释放客户端
        await pool.release(client1)
        await pool.release(client2)


class TestGlobalFunctions:
    """测试全局函数"""
    
    def test_get_default_manager(self):
        """测试获取默认管理器"""
        from src.core.llm_manager import get_default_manager
        
        manager1 = get_default_manager()
        manager2 = get_default_manager()
        
        # 应该返回同一个实例（单例模式）
        assert manager1 is manager2
    
    @pytest.mark.asyncio
    async def test_quick_chat_function(self):
        """测试快速聊天函数"""
        from src.core.llm_manager import quick_chat
        
        env_vars = {
            "REASONING_LLM_MODEL": "glm-z1-flash",
            "REASONING_LLM_API_KEY": "test_key",
            "REASONING_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/",
            "BASIC_LLM_MODEL": "glm-4-flash",
            "BASIC_LLM_API_KEY": "test_key",
            "BASIC_LLM_BASE_URL": "https://open.bigmodel.cn/api/paas/v4/"
        }
        
        with patch.dict(os.environ, env_vars, clear=True):
            with patch('aiohttp.ClientSession.post') as mock_post:
                mock_response = AsyncMock()
                mock_response.json.return_value = {
                    "choices": [{"message": {"content": "快速回答"}}],
                    "usage": {"total_tokens": 80}
                }
                mock_response.status = 200
                mock_post.return_value.__aenter__.return_value = mock_response
                
                response = await quick_chat("简单问题")
                
                assert response["content"] == "快速回答" 