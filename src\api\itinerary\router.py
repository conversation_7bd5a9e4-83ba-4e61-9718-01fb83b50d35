from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker, joinedload
from urllib.parse import quote_plus
from datetime import datetime, date
from src.models.itinerary import (
    itinerary_models as models,
    itinerary_schemas as schemas,
    itinerary_crud as crud
)
from src.core.logger import get_logger
from src.core.config import settings

router = APIRouter()
logger = get_logger('itinerary')

# 构建数据库URL
def get_db_url():
    """构建MySQL数据库URL"""
    mysql_conf = settings.mysql_conf
    password = quote_plus(mysql_conf.password)  # URL编码密码，处理特殊字符
    return f"mysql+pymysql://{mysql_conf.user}:{password}@{mysql_conf.host}:{mysql_conf.port}/{mysql_conf.database}?charset=utf8mb4"

# 创建数据库引擎，添加连接池配置
engine = create_engine(
    get_db_url(),
    pool_size=5,  # 连接池大小
    max_overflow=10,  # 超过pool_size后最多可以创建的连接数
    pool_timeout=30,  # 从连接池获取连接的超时时间
    pool_recycle=1800,  # 连接在池中重用的时间限制（秒）
    echo=False  # 设置为True可以输出SQL语句，用于调试
)

# 创建SessionLocal类
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 数据库依赖项函数
def get_db() -> Session:
    """
    创建数据库会话依赖项。
    每个请求都会创建一个新的数据库会话，请求结束时自动关闭。
    
    Returns:
        Session: SQLAlchemy会话实例
        
    Raises:
        HTTPException: 当数据库连接失败时抛出500错误
    """
    db = SessionLocal()
    try:
        # 测试连接是否有效
        db.execute(text("SELECT 1"))
        yield db
        db.commit()
    except Exception as e:
        db.rollback()
        logger.error("Database connection error", 
                    error=str(e),
                    host=settings.mysql_conf.host,
                    database=settings.mysql_conf.database,
                    exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Database connection error"
        )
    finally:
        db.close()

@router.get("/api/itineraries")
async def get_itineraries(
    user_id: int,
    status: Optional[str] = None,
    page: int = Query(default=1, ge=1),
    page_size: int = Query(default=10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取用户行程列表"""
    try:
        # Initialize CRUD operation
        itinerary_crud = crud.CRUDItinerary(schemas.Itinerary)
        
        # Get itineraries with status display names using SQLAlchemy relationships
        skip = (page - 1) * page_size
        itineraries = itinerary_crud.get_by_user_with_status(
            db, user_id=user_id, skip=skip, limit=page_size, status_key=status
        )
        
        # Format response
        itinerary_list = [
            {
                "id": i["id"],
                "title": i["title"],
                "city_name": i["city_name"],
                "total_days": i["total_days"],
                "start_date": i["start_date"].isoformat() if i["start_date"] else None,
                "cover_image_url": i["cover_image_url"],
                "tags": [tag.name for tag in i["tags"]],
                "status_id": i["status_id"],
                "status_display_name": i["status_display_name"],
                "created_at": i["created_at"].isoformat()
            }
            for i in itineraries
        ]
        
        return {
            "code": 200,
            "data": {
                "total": len(itineraries),
                "list": itinerary_list
            }
        }
    except Exception as e:
        logger.error(f"Error getting itineraries: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/tags")
async def get_tags(db: Session = Depends(get_db)):
    """获取所有可用的行程标签列表"""
    try:
        # Get all tags from database
        tags = db.query(schemas.Tag).all()
        
        return {
            "code": 200,
            "data": [
                {
                    "id": tag.id,
                    "name": tag.name,
                    "category": tag.category
                }
                for tag in tags
            ]
        }
    except Exception as e:
        logger.error(f"Error getting tags: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/api/itineraries/plan")
async def plan_itinerary(
    city_name: str,
    total_days: int,
    tags: List[str] = [],
    db: Session = Depends(get_db)
):
    """AI规划行程"""
    try:
        # TODO: Implement AI planning logic
        # For now, return a mock response
        return {
            "code": 200,
            "data": {
                "id": 1,
                "title": f"{city_name}{total_days}日游",
                "city_name": city_name,
                "total_days": total_days,
                "days": [
                    {
                        "day_number": 1,
                        "summary": "上海博物馆→人民广场→南京路步行街",
                        "pois": [
                            {
                                "sequence": 1,
                                "poi_id": 101,
                                "name": "上海博物馆",
                                "type": "ATTRACTION"
                            }
                        ]
                    }
                ]
            }
        }
    except Exception as e:
        logger.error(f"Error planning itinerary: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/api/itineraries/{id}", response_model=models.ItineraryResponse)
async def get_itinerary_complete_detail(
    id: int,
    db: Session = Depends(get_db)
):
    """获取行程完整详细信息，包括所有关联表数据"""
    try:
        # Get complete itinerary details from CRUD
        itinerary_crud = crud.CRUDItinerary(schemas.Itinerary)
        complete_data = itinerary_crud.get_complete_detail(db, id)
        
        if not complete_data:
            raise HTTPException(status_code=404, detail="Itinerary not found")
        
        return {
            "code": 200,
            "data": complete_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting complete itinerary detail: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/api/itineraries")
async def create_itinerary(
    request: models.ItineraryBase,
    db: Session = Depends(get_db)
):
    """创建新行程"""
    try:
        print(request)
        itinerary_crud = crud.CRUDItinerary(schemas.Itinerary)
        
        # Create itinerary
        itinerary_data = request.dict()
        itinerary = itinerary_crud.create(db, obj_in=itinerary_data)
        
        return {
            "code": 200,
            "data": {
                "id": itinerary.id,
                "message": "行程创建成功"
            }
        }
    except Exception as e:
        logger.error(f"Error creating itinerary: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/api/itineraries/{id}")
async def update_itinerary(
    id: int,
    request: models.ItineraryDetail,
    db: Session = Depends(get_db)
):
    """更新行程信息"""
    try:
        itinerary_crud = crud.CRUDItinerary(schemas.Itinerary)
        
        # Get existing itinerary
        itinerary = itinerary_crud.get(db, id)
        if not itinerary:
            raise HTTPException(status_code=404, detail="Itinerary not found")
        
        itinerary_data = request.dict()
        
        # Update itinerary with tags
        updated_itinerary = itinerary_crud.update(db, db_obj=itinerary, obj_in=itinerary_data)
        
        return {
            "code": 200,
            "data": {
                "id": updated_itinerary.id,
                "message": "行程更新成功"
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating itinerary: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/api/itineraries/{id}/pois")
async def update_itinerary_pois(
    id: int,
    request: models.ItineraryDays,
    db: Session = Depends(get_db)
):
    """更新行程POI - 全量覆盖某天的POI列表"""
    try:
        # Get itinerary with days
        itinerary_crud = crud.CRUDItinerary(schemas.Itinerary)
        itinerary = itinerary_crud.get(db, id)
        
        if not itinerary:
            raise HTTPException(status_code=404, detail="Itinerary not found")
            
        # Update POIs for each day
        for day_update in request.days:
            # Find the corresponding day or create if not exists
            day = next((d for d in itinerary.days if d.day_number == day_update.day_number), None)
            if not day:
                # Create new day if it doesn't exist
                day = schemas.ItineraryDay(
                    itinerary_id=itinerary.id,
                    day_number=day_update.day_number,
                    summary=day_update.summary
                )
                db.add(day)
                db.flush()  # Get the ID
            
            # Delete all existing POI associations for this day
            db.query(schemas.ItineraryDayPOI).filter(
                schemas.ItineraryDayPOI.itinerary_day_id == day.id
            ).delete()
            
            # Create new POI associations
            for poi_data in day_update.pois:
                # Create new association
                day_poi = schemas.ItineraryDayPOI(
                    itinerary_day_id=day.id,
                    poi_id=poi_data.id,
                    sequence=poi_data.sequence,
                    user_notes=poi_data.user_notes
                )
                db.add(day_poi)
            
            # Update day summary if provided
            if day_update.summary:
                day.summary = day_update.summary
        
        db.commit()
        
        return {
            "code": 200,
            "data": {
                "message": "行程POI更新成功",
                "updated_days": [day.day_number for day in request.days]
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating itinerary POIs: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/pois/{id}")
async def get_poi_detail(
    id: int,
    db: Session = Depends(get_db)
):
    """获取POI详情"""
    try:
        poi_crud = crud.CRUDPOI(schemas.POI)
        poi = poi_crud.get(db, id)
        
        if not poi:
            raise HTTPException(status_code=404, detail="POI not found")
            
        return {
            "code": 200,
            "data": {
                "id": poi.id,
                "name": poi.name,
                "type": poi.poi_type.type_key,
                "address": poi.address,
                "latitude": float(poi.latitude),
                "longitude": float(poi.longitude),
                "description": poi.description,
                "images": poi.images,
                "rating": poi.rating,
                "opening_hours": poi.opening_hours,
                "phone_number": poi.phone_number,
                "narrations": poi.narrations
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting POI detail: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/users/{user_id}/stats")
async def get_user_stats(
    user_id: int,
    db: Session = Depends(get_db)
):
    """获取用户行程统计"""
    try:
        # Get user stats from database
        stats = db.query(schemas.UserTripStats).filter_by(user_id=user_id).first()
        
        if not stats:
            return {
                "code": 200,
                "data": {
                    "trip_count": 0,
                    "city_count": 0,
                    "total_mileage": 0,
                    "total_days": 0
                }
            }
            
        return {
            "code": 200,
            "data": {
                "trip_count": stats.trip_count,
                "city_count": stats.city_count,
                "total_mileage": float(stats.total_mileage),
                "total_days": stats.total_days
            }
        }
    except Exception as e:
        logger.error(f"Error getting user stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/users/{user_id}/settings")
async def get_user_settings(
    user_id: int,
    db: Session = Depends(get_db)
):
    """获取用户设置"""
    try:
        settings = db.query(schemas.UserAppSettings).filter_by(user_id=user_id).first()
        
        if not settings:
            raise HTTPException(status_code=404, detail="User settings not found")
            
        return {
            "code": 200,
            "data": {
                "narration_enabled": settings.narration_enabled,
                # "narration_trigger_distance": settings.narration_trigger_distance,
                "in_poi_guide_enabled": settings.in_poi_guide_enabled,
                "proactive_recommend_enabled": settings.proactive_recommend_enabled,
                # "proactive_recommend_mode": settings.proactive_recommend_mode,
                "updated_at": settings.updated_at.isoformat()
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user settings: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/api/users/{user_id}/settings")
async def update_user_settings(
    user_id: int,
    request: models.UserSettings,
    db: Session = Depends(get_db)
):
    """更新用户设置"""
    try:
        settings = db.query(schemas.UserAppSettings).filter_by(user_id=user_id).first()
        
        if not settings:
            settings = schemas.UserAppSettings(user_id=user_id)
            db.add(settings)
            
        # Update settings
        for field, value in request.dict().items():
            setattr(settings, field, value)
            
        settings.updated_at = datetime.utcnow()
        db.commit()
        
        return {
            "code": 200,
            "data": {
                "narration_enabled": settings.narration_enabled,
                # "narration_trigger_distance": settings.narration_trigger_distance,
                "in_poi_guide_enabled": settings.in_poi_guide_enabled,
                "proactive_recommend_enabled": settings.proactive_recommend_enabled,
                # "proactive_recommend_mode": settings.proactive_recommend_mode,
                "updated_at": settings.updated_at.isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error updating user settings: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/vlogs/styles")
async def get_vlog_styles(db: Session = Depends(get_db)):
    """获取视频风格列表"""
    try:
        # TODO: Implement vlog styles database model and CRUD
        # For now, return mock data
        return {
            "code": 200,
            "data": [
                {
                    "id": 1,
                    "name": "旅行日记",
                    "description": "温馨的个人旅行记录风格",
                    "preview_url": "https://example.com/style1_preview.mp4"
                },
                {
                    "id": 2,
                    "name": "时尚大片",
                    "description": "高端时尚的旅行短片风格",
                    "preview_url": "https://example.com/style2_preview.mp4"
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error getting vlog styles: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/vlogs/templates")
async def get_vlog_templates(
    city_name: Optional[str] = None,
    days: Optional[int] = None,
    tags: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取模板库"""
    try:
        itinerary_crud = crud.CRUDItinerary(schemas.Itinerary)
        templates = itinerary_crud.get_templates(db)
        
        # Apply filters
        if city_name:
            templates = [t for t in templates if t.city_name == city_name]
        if days:
            templates = [t for t in templates if t.total_days == days]
        if tags:
            tag_list = tags.split(",")
            templates = [t for t in templates if any(tag.name in tag_list for tag in t.tags)]
            
        return {
            "code": 200,
            "data": [
                {
                    "id": t.id,
                    "title": t.title,
                    "city_name": t.city_name,
                    "total_days": t.total_days,
                    "cover_image_url": t.cover_image_url,
                    "tags": [tag.name for tag in t.tags],
                    "popularity": 4.8  # TODO: Implement popularity calculation
                }
                for t in templates
            ]
        }
    except Exception as e:
        logger.error(f"Error getting vlog templates: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/api/vlogs")
async def create_vlog(
    request: models.VlogCreate,
    db: Session = Depends(get_db)
):
    """请求生成Vlog"""
    try:
        # Create vlog record
        vlog = schemas.AIVlog(
            user_id=request.user_id,
            title=f"Vlog {datetime.now().strftime('%Y-%m-%d')}",
            config=request.dict(),
            status_id=1  # QUEUED
        )
        db.add(vlog)
        db.commit()
        
        return {
            "code": 200,
            "data": {
                "vlog_id": vlog.id,
                "status": "QUEUED",
                "estimated_time": 300
            }
        }
    except Exception as e:
        logger.error(f"Error creating vlog: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/api/vlogs/{id}")
async def get_vlog_status(
    id: int,
    db: Session = Depends(get_db)
):
    """获取Vlog生成状态"""
    try:
        vlog = db.query(schemas.AIVlog).get(id)
        
        if not vlog:
            raise HTTPException(status_code=404, detail="Vlog not found")
            
        return {
            "code": 200,
            "data": {
                "id": vlog.id,
                "status": "COMPLETED" if vlog.status_id == 2 else "IN_PROGRESS",
                "title": vlog.title,
                "final_video_url": vlog.final_video_url,
                "progress": 100 if vlog.status_id == 2 else 50,
                "created_at": vlog.created_at.isoformat(),
                "completed_at": vlog.completed_at.isoformat() if vlog.completed_at else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting vlog status: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")
