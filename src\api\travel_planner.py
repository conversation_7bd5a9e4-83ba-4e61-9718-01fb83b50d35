"""
旅行规划API接口

提供旅行规划的RESTful API和SSE流式接口，支持实时进度反馈。
"""
import asyncio
import json
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, StreamEvent, EventType
)
from src.agents.travel_planner_agent import TravelPlannerAgent
from src.database.mongodb_client import get_mongo_client
from src.core.logger import get_logger

logger = get_logger("travel_planner_api")
router = APIRouter(prefix="/api/travel", tags=["旅行规划"])


class PlanRequest(BaseModel):
    """规划请求模型"""
    query: str
    user_id: str
    session_id: Optional[str] = None


class PlanResponse(BaseModel):
    """规划响应模型"""
    trace_id: str
    status: str
    message: str


@router.post("/plan", response_model=PlanResponse)
async def create_travel_plan(request: PlanRequest):
    """
    创建旅行规划任务
    
    创建一个新的旅行规划任务，返回trace_id用于后续查询进度。
    """
    try:
        trace_id = str(uuid.uuid4())
        
        # 记录规划请求
        mongo_client = await get_mongo_client()
        await mongo_client.log_analytics({
            "event_type": "plan_request",
            "user_id": request.user_id,
            "trace_id": trace_id,
            "properties": {
                "query": request.query,
                "session_id": request.session_id
            }
        })
        
        logger.info(f"创建旅行规划任务: {trace_id}", extra={
            "trace_id": trace_id,
            "user_id": request.user_id,
            "query": request.query
        })
        
        return PlanResponse(
            trace_id=trace_id,
            status="created",
            message="规划任务已创建，请使用SSE接口获取实时进度"
        )
        
    except Exception as e:
        logger.error(f"创建规划任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建规划任务失败")


@router.get("/plan/{trace_id}/stream")
async def stream_travel_plan(trace_id: str, user_id: str, query: str):
    """
    流式获取旅行规划进度
    
    通过Server-Sent Events (SSE) 实时获取旅行规划的进度和结果。
    """
    
    async def event_generator():
        """事件生成器"""
        try:
            # 创建旅行规划Agent
            agent = TravelPlannerAgent()
            
            # 创建规划请求
            plan_request = TravelPlanRequest(
                user_id=user_id,
                query=query
            )
            
            # 发送开始事件
            start_event = {
                "event": "start",
                "data": json.dumps({
                    "trace_id": trace_id,
                    "message": "开始规划旅行行程",
                    "timestamp": datetime.now().isoformat()
                })
            }
            yield f"event: start\ndata: {start_event['data']}\n\n"
            
            # 执行规划并流式返回结果
            async for stream_event in agent.plan_travel(plan_request):
                # 转换为SSE格式
                event_data = {
                    "trace_id": trace_id,
                    "event_id": stream_event.event_id,
                    "event_type": stream_event.event_type.value,
                    "payload": stream_event.payload,
                    "timestamp": stream_event.timestamp.isoformat()
                }
                
                # 发送事件
                yield f"event: {stream_event.event_type.value}\ndata: {json.dumps(event_data, ensure_ascii=False)}\n\n"
                
                # 如果是最终行程，保存到数据库
                if stream_event.event_type == EventType.FINAL_ITINERARY:
                    try:
                        mongo_client = await get_mongo_client()
                        await mongo_client.create_itinerary(stream_event.payload)
                        logger.info(f"行程已保存到数据库: {trace_id}")
                    except Exception as e:
                        logger.error(f"保存行程失败: {str(e)}")
                
                # 添加小延迟，避免过快发送
                await asyncio.sleep(0.1)
            
            # 发送完成事件
            complete_event = {
                "event": "complete",
                "data": json.dumps({
                    "trace_id": trace_id,
                    "message": "旅行规划完成",
                    "timestamp": datetime.now().isoformat()
                })
            }
            yield f"event: complete\ndata: {complete_event['data']}\n\n"
            
        except Exception as e:
            logger.error(f"规划过程中发生错误: {str(e)}", extra={
                "trace_id": trace_id,
                "error": str(e)
            })
            
            # 发送错误事件
            error_event = {
                "event": "error",
                "data": json.dumps({
                    "trace_id": trace_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
            }
            yield f"event: error\ndata: {error_event['data']}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router.get("/plan/{trace_id}")
async def get_travel_plan(trace_id: str):
    """
    获取旅行规划结果
    
    根据trace_id获取完整的旅行规划结果。
    """
    try:
        mongo_client = await get_mongo_client()
        itinerary = await mongo_client.get_itinerary(trace_id)
        
        if not itinerary:
            raise HTTPException(status_code=404, detail="未找到指定的行程")
        
        return {
            "code": 200,
            "data": itinerary,
            "message": "获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取行程失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取行程失败")


@router.get("/user/{user_id}/plans")
async def get_user_plans(
    user_id: str,
    limit: int = 20,
    skip: int = 0,
    status: Optional[str] = None
):
    """
    获取用户的旅行规划列表
    
    分页获取指定用户的所有旅行规划。
    """
    try:
        mongo_client = await get_mongo_client()
        itineraries = await mongo_client.get_user_itineraries(
            user_id=user_id,
            limit=limit,
            skip=skip,
            status=status
        )
        
        return {
            "code": 200,
            "data": {
                "items": itineraries,
                "total": len(itineraries),
                "limit": limit,
                "skip": skip
            },
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取用户行程列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取行程列表失败")


@router.put("/plan/{trace_id}")
async def update_travel_plan(trace_id: str, update_data: Dict[str, Any]):
    """
    更新旅行规划
    
    更新指定的旅行规划信息。
    """
    try:
        mongo_client = await get_mongo_client()
        success = await mongo_client.update_itinerary(trace_id, update_data)
        
        if not success:
            raise HTTPException(status_code=404, detail="未找到指定的行程")
        
        return {
            "code": 200,
            "message": "更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新行程失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新行程失败")


@router.post("/feedback")
async def submit_feedback(feedback_data: Dict[str, Any]):
    """
    提交反馈
    
    用户对旅行规划结果的反馈，用于改进算法。
    """
    try:
        mongo_client = await get_mongo_client()
        
        # 创建反馈记录
        feedback_id = await mongo_client.create_memory({
            "user_id": feedback_data.get("user_id"),
            "memory_type": "feedback",
            "content": feedback_data.get("content", ""),
            "context": feedback_data,
            "importance_score": 2.0,  # 反馈的重要性较高
            "related_itinerary_id": feedback_data.get("trace_id")
        })
        
        # 记录分析数据
        await mongo_client.log_analytics({
            "event_type": "feedback_submitted",
            "user_id": feedback_data.get("user_id"),
            "properties": feedback_data
        })
        
        return {
            "code": 200,
            "data": {"feedback_id": feedback_id},
            "message": "反馈提交成功"
        }
        
    except Exception as e:
        logger.error(f"提交反馈失败: {str(e)}")
        raise HTTPException(status_code=500, detail="提交反馈失败")


@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        mongo_client = await get_mongo_client()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "mongodb": "connected",
                "agent": "ready"
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
