"""
统一配置管理模块

基于 pydantic-settings 实现的配置系统，支持从环境变量和配置文件加载设置。
按角色区分LLM配置，支持 OpenAI 兼容的接口标准。
"""
from typing import Annotated, Optional, Dict
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class MySQLConfig(BaseModel):
    """数据库连接配置"""
    host: str = Field("***********", description="数据库主机地址")
    user: str = Field("root", description="数据库用户名")
    password: str = Field("Fsit#2024", description="数据库密码")
    database: str = Field("dh_tripplanner", description="数据库名")
    port: int = Field(3306, description="数据库端口")


class LLMConfig(BaseModel):
    """
    遵循OpenAI接口标准的LLM配置
    
    支持任何兼容OpenAI API的模型服务，包括本地部署的Ollama、vLLM等。
    """
    model: str = Field(..., description="模型名称")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")
    api_version: Optional[str] = Field(None, description="API版本")


class Settings(BaseSettings):
    """
    应用全局配置类
    
    通过环境变量加载敏感信息，从 config/*.yaml 加载非敏感配置。
    支持按角色区分的LLM配置。
    """
    mysql_conf: MySQLConfig = Field(
        default_factory=MySQLConfig,
        description="MySQL数据库配置"
    )
    
    # 通过 model_config 指定从 .env 文件加载
    model_config = SettingsConfigDict(
        env_file='.env', 
        env_file_encoding='utf-8', 
        extra='ignore',
        env_nested_delimiter='_'  # 支持嵌套环境变量，如 REASONING_LLM_MODEL
    )

    # 按角色区分的LLM配置（字段名匹配环境变量）
    REASONING_LLM_MODEL: str = "123"
    REASONING_LLM_API_KEY: str   = "123"
    REASONING_LLM_BASE_URL: Optional[str] = None
    REASONING_LLM_API_VERSION: Optional[str] = None
    
    BASIC_LLM_MODEL: str = "123"
    BASIC_LLM_API_KEY: str = "123"
    BASIC_LLM_BASE_URL: Optional[str] = None
    BASIC_LLM_API_VERSION: Optional[str] = None
    
    MAP_LLM_MODEL: str = "Qwen3-30B-A3B"
    MAP_LLM_API_KEY: str = "123"
    MAP_LLM_BASE_URL: Optional[str] = None
    MAP_LLM_API_VERSION: Optional[str] = None
    
    
    # 其他基础设施配置
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis连接URL"
    )
    
    @property
    def reasoning_llm(self) -> LLMConfig:
        """构建reasoning LLM配置"""
        return LLMConfig(
            model=self.REASONING_LLM_MODEL,
            api_key=self.REASONING_LLM_API_KEY,
            base_url=self.REASONING_LLM_BASE_URL,
            api_version=self.REASONING_LLM_API_VERSION
        )
    
    @property 
    def basic_llm(self) -> LLMConfig:
        """构建basic LLM配置"""
        return LLMConfig(
            model=self.BASIC_LLM_MODEL,
            api_key=self.BASIC_LLM_API_KEY,
            base_url=self.BASIC_LLM_BASE_URL,
            api_version=self.BASIC_LLM_API_VERSION
        )

    @property
    def map_llm(self) -> LLMConfig:
        """构建map LLM配置"""
        return LLMConfig(
            model=self.MAP_LLM_MODEL,
            api_key=self.MAP_LLM_API_KEY,
            base_url=self.MAP_LLM_BASE_URL,
            api_version=self.MAP_LLM_API_VERSION
        )

    def get_llm_config_by_role(self, role: str) -> LLMConfig:
        """
        根据角色获取对应的LLM配置
        
        Args:
            role: LLM角色，支持 'reasoning' 和 'basic'
            
        Returns:
            对应角色的LLMConfig实例
            
        Raises:
            ValueError: 当角色不存在时
        """
        if role == "reasoning":
            return self.reasoning_llm
        elif role == "basic":
            return self.basic_llm
        elif role == "map":
            return self.map_llm
        else:
            raise ValueError(f"Unknown LLM role: {role}")


# 延迟实例化的全局单例函数
_settings_instance: Optional[Settings] = None


def get_settings() -> Settings:
    """
    获取全局Settings实例（延迟实例化）
    """
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = Settings()
    return _settings_instance


# 为了向后兼容，提供一个settings属性
class SettingsProxy:
    """Settings代理类，提供延迟加载的settings访问"""
    def __getattr__(self, name):
        return getattr(get_settings(), name)


settings = SettingsProxy()


# 使用示例:
# from src.core.config import settings
# planner_llm_config = settings.get_llm_config_by_role("reasoning")
# agent = PlannerAgent(llm_config=planner_llm_config.model_dump())