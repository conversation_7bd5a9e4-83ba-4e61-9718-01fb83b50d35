"""
Map Tool implementation using AutoNavi (Gaode) Map Web API
"""
import json
import requests
from typing import Dict, List, Optional, Union
from dataclasses import dataclass

@dataclass
class Location:
    """Location data structure"""
    latitude: float
    longitude: float
    name: Optional[str] = None
    address: Optional[str] = None

@dataclass
class POIResult:
    """POI search result data structure"""
    id: str
    name: str
    type: str
    address: str
    location: Location
    distance: Optional[float] = None
    rating: Optional[float] = None
    price: Optional[float] = None

class MapTool:
    """
    Map tool implementation using AutoNavi (Gaode) Map API
    Can be wrapped as an AutoGen FunctionTool
    """
    def __init__(self, api_key: str = "cd978c562fe54dd9a11117bfd4a2a3f1", base_url: str = "https://restapi.amap.com/v3"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

    def search_pois(
        self,
        keywords: str,
        city: str,
        type_: Optional[str] = None,
        location: Optional[Location] = None,
        radius: Optional[int] = 3000,
        sort_by: str = "distance",
        price_range: Optional[tuple[float, float]] = None,
        page: int = 1,
        page_size: int = 10
    ) -> List[POIResult]:
        """
        Search POIs (Points of Interest) by keywords and other criteria
        
        Args:
            keywords: Search keywords
            city: City name or code
            type_: POI type category
            location: Center location for nearby search
            radius: Search radius in meters
            sort_by: Sort criteria ("distance" or "rating")
            price_range: Tuple of (min_price, max_price)
            page: Page number
            page_size: Results per page
            
        Returns:
            List of POIResult objects
        """
        url = f"{self.base_url}/place/text"
        
        params = {
            "key": self.api_key,
            "keywords": keywords,
            "city": city,
            "output": "json",
            "offset": page_size,
            "page": page,
            "extensions": "all"
        }
        
        if type_:
            params["types"] = type_
            
        if location:
            params["location"] = f"{location.longitude},{location.latitude}"
            if radius:
                params["radius"] = radius

        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        
        data = response.json()
        if data["status"] != "1":
            raise Exception(f"API Error: {data.get('info', 'Unknown error')}")
            
        pois = []
        for poi in data["pois"]:
            print(poi)
            location_parts = poi["location"].split(",")
            poi_location = Location(
                longitude=float(location_parts[0]),
                latitude=float(location_parts[1]),
                name=poi["name"],
                address=poi["address"]
            )
            # Extract price if available
            price = None
            if "biz_ext" in poi and "cost" in poi["biz_ext"]:
                try:
                    if poi["biz_ext"]["cost"] not in (None, "", []):
                        price = float(poi["biz_ext"]["cost"])
                except (ValueError, TypeError):
                    pass
                    
            # Filter by price range if specified
            # if price_range and price:
            #     min_price, max_price = price_range
            #     if price < min_price or price > max_price:
            #         continue
            
            result = POIResult(
                id=poi["id"],
                name=poi["name"],
                type=poi["type"],
                address=poi["address"],
                location=poi_location,
                distance=0,#float(poi.get("distance", 0)),
                rating=0,#float(poi.get("biz_ext", {}).get("rating", 0)),
                price=price
            )
            pois.append(result)
            
        # Sort results if needed
        if sort_by == "rating":
            pois.sort(key=lambda x: x.rating or 0, reverse=True)
        elif sort_by == "distance":
            pois.sort(key=lambda x: x.distance or float('inf'))
            
        return pois
        
    def get_route(
        self,
        origin: Location,
        destination: Location,
        waypoints: Optional[List[Location]] = None,
        transport_mode: str = "driving",
        city: str = "010",
        cityd: str = "010"
    ) -> Dict:
        """
        Get route between locations
        
        Args:
            origin: Starting location
            destination: Ending location
            waypoints: Optional list of waypoints
            transport_mode: Mode of transport ("driving", "walking", "transit", "cycling")
            
        Returns:
            Dict containing route information
        """
        url = f"{self.base_url}/direction/{transport_mode}"
        if transport_mode == "transit":
            url = f"{self.base_url}/direction/{transport_mode}/integrated"

        params = {
            "key": self.api_key,
            "origin": f"{origin.longitude},{origin.latitude}",
            "destination": f"{destination.longitude},{destination.latitude}",
            "output": "json",
            "extensions": "all",
            "city": city,
            "cityd": cityd,
        }

        if waypoints:
            waypoints_str = ";".join(
                [f"{point.longitude},{point.latitude}" for point in waypoints]
            )
            params["waypoints"] = waypoints_str
            
        response = requests.get(url, params=params, headers=self.headers)
        response.raise_for_status()
        
        data = response.json()
        if data["status"] != "1":
            raise Exception(f"API Error: {data.get('info', 'Unknown error')}")
            
        return data["route"]

# Example of how to wrap this as an AutoGen FunctionTool:
"""
from autogen import FunctionTool

map_tool = MapTool(api_key="your_api_key")

search_pois_tool = FunctionTool(
    name="search_pois",
    description="Search for points of interest (POIs) like restaurants, hotels, attractions etc.",
    func=map_tool.search_pois,
    async_fn=False
)

get_route_tool = FunctionTool(
    name="get_route",
    description="Get route directions between two locations",
    func=map_tool.get_route,
    async_fn=False
)
"""
